<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CongdapImportTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test successful Excel import
     */
    public function test_successful_excel_import()
    {
        Storage::fake('local');

        // Create a mock Excel file
        $file = UploadedFile::fake()->create('test_congdap.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/taisan/congdap/import', [
            'file' => $file,
            'batch_size' => 50,
            'skip_errors' => false
        ]);

        // The response structure should match what the frontend expects
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'statistics' => [
                         'total_rows',
                         'processed_rows',
                         'successful_imports',
                         'failed_imports',
                         'success_rate'
                     ],
                     'performance' => [
                         'processing_time_ms',
                         'memory_usage_mb',
                         'average_time_per_row'
                     ],
                     'errors',
                     'summary'
                 ]);
    }

    /**
     * Test import with invalid file type
     */
    public function test_import_with_invalid_file_type()
    {
        $file = UploadedFile::fake()->create('test.txt', 100, 'text/plain');

        $response = $this->postJson('/api/taisan/congdap/import', [
            'file' => $file
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['file']);
    }

    /**
     * Test import with oversized file
     */
    public function test_import_with_oversized_file()
    {
        $file = UploadedFile::fake()->create('large_file.xlsx', 11000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'); // 11MB

        $response = $this->postJson('/api/taisan/congdap/import', [
            'file' => $file
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['file']);
    }

    /**
     * Test import without file
     */
    public function test_import_without_file()
    {
        $response = $this->postJson('/api/taisan/congdap/import', []);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['file']);
    }

    /**
     * Test import with custom batch size
     */
    public function test_import_with_custom_batch_size()
    {
        Storage::fake('local');

        $file = UploadedFile::fake()->create('test_congdap.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/taisan/congdap/import', [
            'file' => $file,
            'batch_size' => 25,
            'skip_errors' => true
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ]);
    }

    /**
     * Test that import route exists and is accessible
     */
    public function test_import_route_exists()
    {
        $response = $this->post('/api/taisan/congdap/import');
        
        // Should return validation error, not 404
        $this->assertNotEquals(404, $response->getStatusCode());
    }
}
