<?php

namespace App\Exceptions\Role;

use Exception;

class RoleValidationException extends Exception
{
    protected $errors;

    public function __construct(string $message, array $errors = [])
    {
        parent::__construct($message);
        $this->errors = $errors;
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'ROLE_VALIDATION_ERROR',
            'errors' => $this->errors
        ], 422);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
