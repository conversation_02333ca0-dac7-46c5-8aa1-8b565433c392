<?php

namespace App\Http\Resources\Xaydung;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Congtrinh_xdResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Xử lý geometry trước
        $geometry = null;
        if (isset($this->geom) && is_string($this->geom)) {
             $decodedGeom = json_decode($this->geom);
             $geometry = (json_last_error() === JSON_ERROR_NONE) ? $decodedGeom : null;
        }

        return [
            'type' => 'Feature',
            'id' => $this->id, // id từ Congdap
            'geometry' => $geometry, // Geometry đã xử lý
            'properties' => [
                'stt_thua' => $this->stt_thua,
                'id_xa' => $this->id_xa,
                // <<< THAY ĐỔI: Truy cập qua relationship đã load >>>
                /* 'tenxa' => $this->whenLoaded('xa', $this->xa->tenxa ?? null),
                'nguyengia' => $this->whenLoaded('quyettoan', $this->quyettoan->nguyengia ?? null), */
                // --- Các trường còn lại của Congdap ---
                'sh_tobando' => $this->sh_tobando,
                'dientich_m2' =>(float) $this->dientich_m2,
                'nam_capnhat' => $this->nam_capnhat
            ]
        ];
    }
}
