<?php

namespace App\Services;

use App\Exceptions\Role\RoleNotFoundException;
use App\Exceptions\Role\RoleValidationException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Eloquent\Builder;

class RoleService
{
    public function getList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $cacheKey = 'roles_list_' . md5(serialize($filters) . $perPage);

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            return Cache::tags(['roles-list'])->remember($cacheKey, 300, function () use ($filters, $perPage) {
                $query = Role::with(['permissions']);

                $this->applyFilters($query, $filters);

                return $query->paginate($perPage);
            });
        } else {
            // Fallback: sử dụng cache thông thường với version key
            $versionKey = 'roles_version';
            $version = Cache::get($versionKey, 0);
            $versionedCacheKey = "{$cacheKey}_v{$version}";

            return Cache::remember($versionedCacheKey, 300, function () use ($filters, $perPage) {
                $query = Role::with(['permissions']);

                $this->applyFilters($query, $filters);

                return $query->paginate($perPage);
            });
        }
    }

    public function getById($identifier): Role
    {
        $cacheKey = "role_" . (is_numeric($identifier) ? "id_{$identifier}" : "name_{$identifier}");

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            $role = Cache::tags(['roles-list'])->remember($cacheKey, 300, function () use ($identifier) {
                return $this->findRoleByIdentifier($identifier);
            });
        } else {
            // Fallback: sử dụng cache thông thường với version key
            $versionKey = 'roles_version';
            $version = Cache::get($versionKey, 0);
            $versionedCacheKey = "{$cacheKey}_v{$version}";

            $role = Cache::remember($versionedCacheKey, 300, function () use ($identifier) {
                return $this->findRoleByIdentifier($identifier);
            });
        }

        if (!$role) {
            throw new RoleNotFoundException($identifier);
        }

        return $role;
    }

    public function create(array $data): Role
    {
        // Extract permissions
        $permissions = $data['permissions'] ?? [];
        unset($data['permissions']);

        $role = Role::create($data);

        // Assign permissions if provided
        if (!empty($permissions)) {
            $role->syncPermissions($permissions);
        }

        // Load permissions for response
        $role->load('permissions');

        return $role;
    }

    public function update($identifier, array $data): Role
    {
        $role = $this->findRoleByIdentifier($identifier);

        if (!$role) {
            throw new RoleNotFoundException($identifier);
        }

        // Extract permissions
        $permissions = $data['permissions'] ?? null;
        unset($data['permissions']);

        // Update role data
        $role->update($data);

        // Update permissions if provided
        if ($permissions !== null) {
            $role->syncPermissions($permissions);
        }

        // Load permissions for response
        $role->load('permissions');

        return $role;
    }

    public function updatePermissions($identifier, array $permissionIds): Role
    {
        $role = $this->findRoleByIdentifier($identifier);

        if (!$role) {
            throw new RoleNotFoundException($identifier);
        }

        // Sync permissions
        $role->syncPermissions($permissionIds);

        // Load permissions for response
        $role->load('permissions');

        return $role;
    }

    public function delete($identifier): bool
    {
        $role = $this->findRoleByIdentifier($identifier);

        if (!$role) {
            throw new RoleNotFoundException($identifier);
        }

        // Check if role can be deleted (not system role)
        if (!($role->is_custom ?? true)) {
            throw new RoleValidationException('Không thể xóa vai trò hệ thống');
        }

        return $role->delete();
    }

    public function getPermissions($identifier)
    {
        $role = $this->getById($identifier);
        return $role->permissions;
    }

    protected function findRoleByIdentifier($identifier): ?Role
    {
        $query = Role::with(['permissions']);

        if (is_numeric($identifier)) {
            return $query->find($identifier);
        } else {
            return $query->where('name', $identifier)->first();
        }
    }

    protected function applyFilters(Builder $query, array $filters): void
    {
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('vi_name', 'ILIKE', "%{$search}%")
                  ->orWhere('description', 'ILIKE', "%{$search}%");
            });
        }

        if (isset($filters['is_custom'])) {
            $query->where('is_custom', $filters['is_custom']);
        }

        if (!empty($filters['permission'])) {
            $query->whereHas('permissions', function ($q) use ($filters) {
                $q->where('name', $filters['permission']);
            });
        }

        if (!empty($filters['created_from'])) {
            $query->where('created_at', '>=', $filters['created_from']);
        }

        if (!empty($filters['created_to'])) {
            $query->where('created_at', '<=', $filters['created_to']);
        }
    }
}
