<?php

namespace App\Exceptions\User;

use Exception;

class UserPermissionException extends Exception
{
    public function __construct(string $message = "Bạn không có quyền thực hiện hành động này")
    {
        parent::__construct($message);
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'USER_PERMISSION_DENIED'
        ], 403);
    }
}
