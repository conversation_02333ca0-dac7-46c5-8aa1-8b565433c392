<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): JsonResponse
    {
        return response()->json([
            'message' => 'Login endpoint - use POST to authenticate',
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): JsonResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        return response()->json([
            'message' => 'Login successful',
            'user' => $request->user()
        ]);
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): JsonResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json([
            'message' => 'Logout successful'
        ]);
    }

    /*
    public function loginAsGuest(Request $request): RedirectResponse
    {
        $guest = User::where('email', '<EMAIL>')->first();

        if ($guest) {
            Auth::login($guest); // Đăng nhập thủ công
            $request->session()->regenerate();

            return redirect()->intended(route('/', absolute: false));
        }

        return redirect()->route('login')->withErrors(['email' => 'Không tìm thấy tài khoản Guest.']);
    }
    */
}
