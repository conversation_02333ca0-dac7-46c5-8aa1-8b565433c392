<?php

namespace App\Services;

use App\Repositories\Contracts\Congtrinh_xdRepositoryInterface;
use App\Exceptions\Xaydung\Congtrinh_xdNotFoundException;
use App\Exceptions\Xaydung\InvalidGeometryException;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\API\Xaydung\Congtrinh_xd;

class Congtrinh_xdService
{
    protected $repository;

    public function __construct(Congtrinh_xdRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function getList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->paginate($filters, $perPage);
    }

    /**
     * Lấy danh sách chỉ bao gồm geometry (không có thuộc tính)
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function getGeometryList(array $filters = []): LengthAwarePaginator
    {
        return $this->repository->paginateGeometryOnly($filters);
    }

    /**
     * L<PERSON>y danh sách chỉ bao gồm thuộc tính (không có geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function getAttributesList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->paginateAttributesOnly($filters, $perPage);
    }

    public function getById(string $id): Congtrinh_xd
    {
        $congtrinh_xd = $this->repository->findById($id);

        if (!$congtrinh_xd) {
            throw new Congtrinh_xdNotFoundException($id);
        }

        return $congtrinh_xd;
    }

    public function create(array $data): Congtrinh_xd
    {
        $this->validateGeometry($data);
        return $this->repository->create($data);
    }

    public function update(string $id, array $data): Congtrinh_xd
    {
        if (isset($data['geometry'])) {
            $this->validateGeometry($data);
        }

        $updated = $this->repository->update($id, $data);

        if (!$updated) {
            throw new Congtrinh_xdNotFoundException($id);
        }

        return $updated;
    }

    public function delete(string $id): bool
    {
        if (!$this->repository->checkExists($id)) {
            throw new Congtrinh_xdNotFoundException($id);
        }

        return $this->repository->delete($id);
    }

    protected function validateGeometry(array $data): void
    {
        if (!isset($data['geometry']) ||
            !isset($data['geometry']['type']) ||
            !isset($data['geometry']['coordinates'])) {
            throw new InvalidGeometryException();
        }
    }
}