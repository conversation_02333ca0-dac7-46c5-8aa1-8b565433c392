<?php

namespace App\Http\Requests\Xaydung;

use Illuminate\Foundation\Http\FormRequest;

class StoreCongtrinh_xdRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'type'                => 'sometimes|string|in:Feature', // GeoJSON Feature type
            // 'id'               => 'sometimes|string', // ID của GeoJSON Feature, thường không cần validate ở đây vì ID model được xử lý riêng
            'properties'          => 'required|array',
            'properties.stt_thua'    => 'nullable|string|max:100',
            'properties.id_xa'    => 'nullable|string|max:5',
            'properties.sh_tobando' => 'nullable|string|max:50',
            'properties.nam_capnhat'   => 'nullable|integer',
            'properties.dientich_m2'   => 'nullable|numeric',
            'geometry'            => ['required', 'array', function($attribute, $value, $fail) {
                if (!isset($value['type']) || !isset($value['coordinates'])) {
                    $fail('The geometry must be a valid GeoJSON object with type and coordinates.');
                }
            }]
        ];
    }

    public function messages(): array
    {
        return [
            'properties.required' => 'Trường properties là bắt buộc.',
            'properties.array'    => 'Trường properties phải là một object.',
            'geometry.required'   => 'Geometry là trường bắt buộc.',
            'geometry.array'      => 'Geometry phải là một GeoJSON object hợp lệ.',
            // Thêm messages cho các trường con của properties nếu cần
            // Ví dụ:
            'properties.stt_thua.string' => 'Số thứ tự thửa phải là chuỗi không quá 100 ký tự.',
            'properties.sh_tobando.string' => 'Số hiệu tờ bản đồ phải là chuỗi không quá 50 ký tự.',
            'properties.dientich_m2.integer' => 'Năm xây dựng phải là số dương.',
        ];
    }
}