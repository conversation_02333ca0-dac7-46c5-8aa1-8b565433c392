<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;

trait ApiResponseTrait
{
    /**
     * Tạo response thành công
     */
    protected function successResponse($data, string $message = '', int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $data,
            'message' => $message,
            'code' => $code
        ], $code);
    }

    /**
     * Tạo response lỗi
     */
    protected function errorResponse(string $message, int $code, $errors = null): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'code' => $code,
            'errors' => $errors
        ], $code);
    }

    /**
     * Tạo response cho collection có phân trang
     */
    protected function paginatedResponse($paginator, $resource, string $message = ''): JsonResponse
    {
        $data = $resource::collection($paginator->items());
        
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'meta' => [
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Tạo response cho GeoJSON collection có phân trang
     */
    protected function paginatedGeoJsonResponse($paginator, $resource, string $message = ''): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => [
                'type' => 'FeatureCollection',
                'features' => $resource::collection($paginator->items())
            ],
            'meta' => [
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'last_page' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
            ],
            'links' => [
                'first' => $paginator->url(1),
                'last' => $paginator->url($paginator->lastPage()),
                'prev' => $paginator->previousPageUrl(),
                'next' => $paginator->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Tạo response cho validation errors
     */
    protected function validationErrorResponse($errors): JsonResponse
    {
        return $this->errorResponse(
            message: 'Dữ liệu không hợp lệ',
            code: 422,
            errors: $errors
        );
    }

    /**
     * Tạo response cho created
     */
    protected function createdResponse($data, string $message = 'Tạo mới thành công'): JsonResponse
    {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Tạo response cho updated
     */
    protected function updatedResponse($data, string $message = 'Cập nhật thành công'): JsonResponse
    {
        return $this->successResponse($data, $message);
    }

    /**
     * Tạo response cho deleted
     */
    protected function deletedResponse(string $message = 'Xóa thành công'): JsonResponse
    {
        return $this->successResponse(null, $message, 204);
    }
}