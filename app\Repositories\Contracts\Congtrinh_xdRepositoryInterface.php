<?php

namespace App\Repositories\Contracts;

use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\API\Xaydung\Congtrinh_xd;

interface Congtrinh_xdRepositoryInterface
{
    public function paginate(array $filters = [], int $perPage = 15): LengthAwarePaginator;
    public function paginateGeometryOnly(array $filters = []): LengthAwarePaginator;
    public function paginateAttributesOnly(array $filters = [], int $perPage = 15): LengthAwarePaginator;
    public function findById(string $id): ?Congtrinh_xd;
    public function create(array $data): Congtrinh_xd;
    public function bulkCreate(array $dataArray): array;
    public function update(string $id, array $data): ?Congtrinh_xd;
    public function delete(string $id): bool;
    public function checkExists(string $id): bool;
}