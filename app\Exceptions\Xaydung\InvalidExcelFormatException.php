<?php

namespace App\Exceptions\Xaydung;

use Exception;

class InvalidExcelFormatException extends Exception
{
    public function __construct(string $message = 'File Excel không đúng định dạng hoặc bị lỗi')
    {
        parent::__construct($message);
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'INVALID_EXCEL_FORMAT',
            'data' => [
                'errors' => [
                    [
                        'row' => 0,
                        'error' => $this->getMessage()
                    ]
                ]
            ]
        ], 422);
    }
}
