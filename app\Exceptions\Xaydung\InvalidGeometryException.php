<?php

namespace App\Exceptions\Xaydung;

use Exception;

class InvalidGeometryException extends Exception
{
    public function __construct(string $message = "Dữ liệu geometry không hợp lệ")
    {
        parent::__construct($message);
    }

    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'code' => 'INVALID_GEOMETRY'
        ], 422);
    }
}