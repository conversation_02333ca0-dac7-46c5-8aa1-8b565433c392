<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('action', 100); // Loại hành động: login, create, update, delete, etc.
            $table->text('description'); // Mô tả chi tiết hành động
            $table->string('model_type')->nullable(); // Loại model bị tác động (App\Models\User, etc.)
            $table->string('model_id')->nullable(); // ID của model bị tác động
            $table->json('old_values')->nullable(); // <PERSON>i<PERSON> trị cũ (cho update/delete)
            $table->json('new_values')->nullable(); // Gi<PERSON> trị mới (cho create/update)
            $table->string('ip_address', 45)->nullable(); // IPv4 hoặc IPv6
            $table->text('user_agent')->nullable(); // Thông tin trình duyệt/thiết bị
            $table->timestamps();

            // Indexes để tối ưu query
            $table->index(['user_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index(['model_type', 'model_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activity_logs');
    }
};
