<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $userId = $this->route('user') ?? $this->route('id');
        
        return [
            'name' => 'sometimes|string|max:255',
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users')->ignore($userId)
            ],
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'roles' => 'nullable|array',
            'roles.*' => 'string|exists:roles,name',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|exists:permissions,name',
        ];
    }

    public function messages(): array
    {
        return [
            'name.string' => 'Tên người dùng phải là chuỗi.',
            'name.max' => 'Tên người dùng không được vượt quá 255 ký tự.',
            'email.string' => 'Email phải là chuỗi.',
            'email.email' => 'Email phải có định dạng hợp lệ.',
            'email.max' => 'Email không được vượt quá 255 ký tự.',
            'email.unique' => 'Email này đã được sử dụng.',
            'password.confirmed' => 'Xác nhận mật khẩu không khớp.',
            'roles.array' => 'Vai trò phải là một mảng.',
            'roles.*.string' => 'Vai trò phải là chuỗi.',
            'roles.*.exists' => 'Vai trò không tồn tại.',
            'permissions.array' => 'Quyền phải là một mảng.',
            'permissions.*.string' => 'Quyền phải là chuỗi.',
            'permissions.*.exists' => 'Quyền không tồn tại.',
        ];
    }
}
