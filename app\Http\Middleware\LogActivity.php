<?php

namespace App\Http\Middleware;

use App\Models\ActivityLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log for authenticated users and successful responses
        if (Auth::check() && $response->getStatusCode() < 400) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Log the activity
     */
    private function logActivity(Request $request, Response $response): void
    {
        try {
            $method = $request->method();
            $path = $request->path();
            $user = Auth::user();

            // Skip logging for certain paths
            $skipPaths = [
                'api/activity-logs',
                'api/activity-logs/filters',
                'sanctum/csrf-cookie',
                'livewire',
            ];

            foreach ($skipPaths as $skipPath) {
                if (str_starts_with($path, $skipPath)) {
                    return;
                }
            }

            // Determine action based on HTTP method and path
            $action = $this->determineAction($method, $path);
            $description = $this->generateDescription($method, $path, $request);

            // Create activity log
            ActivityLog::create([
                'user_id' => $user->id,
                'action' => $action,
                'description' => $description,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'model_type' => $this->extractModelType($path),
                'model_id' => $this->extractModelId($path, $request),
            ]);

        } catch (\Exception $e) {
            // Don't let logging errors break the application
            Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }

    /**
     * Determine action based on HTTP method and path
     */
    private function determineAction(string $method, string $path): string
    {
        if (str_contains($path, 'login')) {
            return 'login';
        }

        if (str_contains($path, 'logout')) {
            return 'logout';
        }

        if (str_contains($path, 'export')) {
            return 'export';
        }

        if (str_contains($path, 'import')) {
            return 'import';
        }

        return match ($method) {
            'GET' => 'view',
            'POST' => 'create',
            'PUT', 'PATCH' => 'update',
            'DELETE' => 'delete',
            default => 'unknown'
        };
    }

    /**
     * Generate description for the activity
     */
    private function generateDescription(string $method, string $path, Request $request): string
    {
        $user = Auth::user();
        $userName = $user->name;

        if (str_contains($path, 'login')) {
            return "Người dùng {$userName} đăng nhập vào hệ thống";
        }

        if (str_contains($path, 'logout')) {
            return "Người dùng {$userName} đăng xuất khỏi hệ thống";
        }

        // Extract resource name from path
        $resourceName = $this->extractResourceName($path);

        return match ($method) {
            'GET' => "Người dùng {$userName} xem danh sách {$resourceName}",
            'POST' => "Người dùng {$userName} tạo mới {$resourceName}",
            'PUT', 'PATCH' => "Người dùng {$userName} cập nhật {$resourceName}",
            'DELETE' => "Người dùng {$userName} xóa {$resourceName}",
            default => "Người dùng {$userName} thực hiện hành động trên {$resourceName}"
        };
    }

    /**
     * Extract resource name from path
     */
    private function extractResourceName(string $path): string
    {
        $resourceMap = [
            'congdap' => 'cống đập',
            'trambom' => 'trạm bơm',
            'kenhmuong' => 'kênh mương',
            'users' => 'người dùng',
            'roles' => 'vai trò',
            'permissions' => 'quyền',
            'reports' => 'báo cáo',
        ];

        foreach ($resourceMap as $key => $name) {
            if (str_contains($path, $key)) {
                return $name;
            }
        }

        return 'tài nguyên';
    }

    /**
     * Extract model type from path
     */
    private function extractModelType(string $path): ?string
    {
        $modelMap = [
            'congdap' => 'App\\Models\\API\\Taisan\\Congdap',
            'trambom' => 'App\\Models\\API\\Taisan\\Trambom',
            'kenhmuong' => 'App\\Models\\API\\Taisan\\Kenhmuong',
            'users' => 'App\\Models\\User',
            'roles' => 'Spatie\\Permission\\Models\\Role',
            'permissions' => 'Spatie\\Permission\\Models\\Permission',
        ];

        foreach ($modelMap as $key => $model) {
            if (str_contains($path, $key)) {
                return $model;
            }
        }

        return null;
    }

    /**
     * Extract model ID from path or request
     */
    private function extractModelId(string $path, Request $request): ?string
    {
        // Try to extract ID from URL path (e.g., /api/users/123)
        if (preg_match('/\/([A-Z0-9]+)$/', $path, $matches)) {
            return $matches[1];
        }

        // Try to get ID from request data
        if ($request->has('id')) {
            return $request->input('id');
        }

        return null;
    }
}
