<?php

namespace App\Http\Requests\Role;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRolePermissionsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'permissions' => 'present|array', // 'present' đảm bảo key tồn tại, kể cả khi là mảng rỗng
            'permissions.*' => 'integer|exists:permissions,id',
        ];
    }

    public function messages(): array
    {
        return [
            'permissions.present' => 'Trường quyền là bắt buộc.',
            'permissions.array' => 'Quyền phải là một mảng.',
            'permissions.*.integer' => 'ID quyền phải là số nguyên.',
            'permissions.*.exists' => 'Quyền không tồn tại.',
        ];
    }
}
