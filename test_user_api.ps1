# Test script for User API endpoints
$baseUrl = "http://127.0.0.1:8000/api/users"

Write-Host "=== Testing User API Endpoints ===" -ForegroundColor Green

# Test 1: Get all users
Write-Host "`n1. Testing GET /api/users" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $json = $response.Content | ConvertFrom-Json
    Write-Host "Success: $($json.success)"
    Write-Host "Message: $($json.message)"
    Write-Host "Total users: $($json.meta.total)"
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get users with pagination
Write-Host "`n2. Testing GET /api/users with pagination" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl?per_page=2&page=1" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $json = $response.Content | ConvertFrom-Json
    Write-Host "Per page: $($json.meta.per_page)"
    Write-Host "Current page: $($json.meta.current_page)"
    Write-Host "Total: $($json.meta.total)"
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Search users
Write-Host "`n3. Testing GET /api/users with search" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl?search=A" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $json = $response.Content | ConvertFrom-Json
    Write-Host "Search results: $($json.data.Count) users found"
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get specific user
Write-Host "`n4. Testing GET /api/users/1" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/1" -Method GET
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    $json = $response.Content | ConvertFrom-Json
    Write-Host "User name: $($json.data.name)"
    Write-Host "User email: $($json.data.email)"
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Get non-existent user (should return 404)
Write-Host "`n5. Testing GET /api/users/999 (should return 404)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/999" -Method GET
    Write-Host "Unexpected success: $($response.StatusCode)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "Expected 404 error: User not found" -ForegroundColor Green
    } else {
        Write-Host "Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 6: Test response structure
Write-Host "`n6. Testing response structure compatibility" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method GET
    $json = $response.Content | ConvertFrom-Json
    
    # Check required fields for Vue.js component
    $requiredFields = @('id', 'name', 'email', 'username', 'role', 'unit', 'status', 'created_at')
    $firstUser = $json.data[0]
    
    Write-Host "Checking required fields for Vue.js compatibility:"
    foreach ($field in $requiredFields) {
        if ($firstUser.PSObject.Properties.Name -contains $field) {
            Write-Host "  ✓ $field" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $field (missing)" -ForegroundColor Red
        }
    }
    
    # Check pagination structure
    $requiredMetaFields = @('current_page', 'per_page', 'total', 'last_page', 'from', 'to')
    Write-Host "`nChecking pagination metadata:"
    foreach ($field in $requiredMetaFields) {
        if ($json.meta.PSObject.Properties.Name -contains $field) {
            Write-Host "  ✓ $field" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $field (missing)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test completed ===" -ForegroundColor Green
