<?php

namespace App\Models\API\Taisan;

use Illuminate\Database\Eloquent\Model;
use App\Models\API\Taisan\Congdap;

class Quyettoan extends Model
{
    protected $connection = 'pgsql'; // <<< Hoặc connection mặc định của bạn
    protected $table = 'taisan.quyettoan'; // <<< Tên bảng chính xác
    protected $primaryKey = 'id';
    public $incrementing = false;
    public $timestamps = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'nguyengia',
        'qd_dautu',
        'qd_quyettoan',
    ];

    public function congdap()
    {
        return $this->belongsTo(Congdap::class, 'id_congdap', 'id');
    }
}
