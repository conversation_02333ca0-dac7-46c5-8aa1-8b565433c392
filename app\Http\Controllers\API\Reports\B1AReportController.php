<?php

namespace App\Http\Controllers\API\Reports;

use App\Http\Controllers\Controller;
use App\Models\API\Taisan\Congdap;
use App\Models\API\Taisan\Trambom;
use App\Models\API\Taisan\Kenhmuong;
use App\Traits\ApiResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpWord\TemplateProcessor;

class B1AReportController extends Controller
{
    use ApiResponseTrait;

    public function index(Request $request)
    {
        try {
            // Validate input parameters
            $validatedData = $request->validate([
                'ten' => 'nullable|string',
                'ma_donvi' => 'nullable|string',
                'thon_xom' => 'nullable|string',
                'xa_phuong' => 'nullable|string',
                'quan_huyen' => 'nullable|string',
                'tinh_tp' => 'nullable|string',
            ]);

            $currentYear = Carbon::now()->year;

            // Base query for each asset type with common fields
            $commonSelect = "
                ten,
                quymo_ct as quy_mo,
                loai_ct as loai_cong_trinh,
                nam_sd as nam_su_dung,
                dt_dat as dien_tich_dat,
                COALESCE(q.nguyengia, 0) as nguyen_gia,
                tinhtrang as tinh_trang,
                chuthich as ghi_chu,
                r.tenxa as xa_phuong,
                r.tenhuyen as quan_huyen,
                CASE 
                    WHEN nam_sd IS NOT NULL AND nam_sd <> 0 THEN
                        LEAST( -- Khấu hao lũy kế không được vượt quá nguyên giá
                            COALESCE(q.nguyengia, 0),
                            GREATEST(0, ($currentYear - nam_sd::integer) * 0.04 * COALESCE(q.nguyengia, 0)) -- Đảm bảo khấu hao không âm
                        )
                    ELSE 0
                END as khau_hao_tinh,
                CASE 
                    WHEN nam_sd IS NOT NULL AND nam_sd <> 0 AND q.nguyengia IS NOT NULL THEN 
                        GREATEST(0, q.nguyengia - ($currentYear - nam_sd::integer) * 0.04 * q.nguyengia)
                    ELSE COALESCE(q.nguyengia, 0)
                END as gia_tri_con_lai
            ";

            // Query for each asset type
            $congdap = DB::connection('pgsql')
                ->table('taisan.congdap as c')
                ->leftJoin('taisan.quyettoan as q', 'c.id_qt', '=', 'q.id')
                ->leftJoin('basemap.rg_xa as r', 'c.id_xa', '=', 'r.id')
                ->selectRaw($commonSelect . ", c.id_qt as id_quyettoan")
                ->addSelect(DB::raw("'Cống đập' as loai_tai_san"));

            $trambom = DB::connection('pgsql')
                ->table('taisan.trambom as t')
                ->leftJoin('taisan.quyettoan as q', 't.id_qt', '=', 'q.id')
                ->leftJoin('basemap.rg_xa as r', 't.id_xa', '=', 'r.id')
                ->selectRaw($commonSelect . ", t.id_qt as id_quyettoan")
                ->addSelect(DB::raw("'Trạm bơm' as loai_tai_san"));

            $kenhmuong = DB::connection('pgsql')
                ->table('taisan.kenhmuong as k')
                ->leftJoin('taisan.quyettoan as q', 'k.id_qt', '=', 'q.id')
                ->leftJoin('basemap.rg_xa as r', 'k.id_xa', '=', 'r.id')
                ->selectRaw($commonSelect . ", k.id_qt as id_quyettoan")
                ->addSelect(DB::raw("'Kênh mương' as loai_tai_san"));

            // Apply filters if provided
            foreach ([$congdap, $trambom, $kenhmuong] as $query) {
                if (!empty($validatedData['ten'])) {
                    $query->where('ten', 'ilike', '%' . $validatedData['ten'] . '%');
                }
                if (!empty($validatedData['xa_phuong'])) {
                    $query->where('r.tenxa', 'ilike', '%' . $validatedData['xa_phuong'] . '%');
                }
                if (!empty($validatedData['quan_huyen'])) {
                    $query->where('r.tenhuyen', 'ilike', '%' . $validatedData['quan_huyen'] . '%');
                }
            }

            // Union all queries and get results
            $results = $trambom
                ->union($congdap)
                ->union($kenhmuong)
                ->orderBy('loai_tai_san') // Sắp xếp kết quả theo loại tài sản
                ->get();

            // Calculate totals
            $totals = [
                'total_count' => $results->count(),
                'total_area' => $results->sum('dien_tich_dat'),
                'total_original_value' => $results->sum('nguyen_gia'),
                'total_depreciation' => $results->sum('khau_hao_tinh'),
                'total_remaining_value' => $results->sum('gia_tri_con_lai'),
                'total_active' => $results->filter(fn($item) => isset($item->tinh_trang) && mb_strtolower(trim($item->tinh_trang)) !== 'hư hỏng')->count(),
                'total_inactive' => $results->filter(fn($item) => isset($item->tinh_trang) && mb_strtolower(trim($item->tinh_trang)) === 'hư hỏng')->count(),
            ];

            // Group by asset type
            $byType = $results->groupBy('loai_tai_san')
                ->map(function ($items) {
                    return [
                        'count' => $items->count(),
                        'total_area' => $items->sum('dien_tich_dat'),
                        'total_value' => $items->sum('nguyen_gia'),
                    ];
                });

            return $this->successResponse([
                'data' => $results,
                'summary' => [
                    'totals' => $totals,
                    'by_type' => $byType
                ],
                'metadata' => [
                    'report_date' => Carbon::now()->format('Y-m-d'),
                    'report_type' => 'B1A',
                    'report_name' => 'Báo cáo kê khai tài sản kết cấu hạ tầng thủy lợi'
                ]
            ], 'Lấy dữ liệu báo cáo B1A thành công');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    public function export(Request $request) // Đảm bảo Request $request được inject
    {
        try {
            // Lấy dữ liệu báo cáo
            $response = $this->index($request); // Truyền request nếu index() cũng cần lọc theo ngày hoặc các tham số khác
            $reportData = json_decode($response->getContent(), true);

            if (!isset($reportData['data']) || !isset($reportData['data']['data'])) {
                throw new \Exception('Dữ liệu báo cáo không hợp lệ.');
            }            // Kiểm tra template tồn tại
            $templatePath = storage_path('app/templates/reports/B1A-Kekhai.docx');
            if (!file_exists($templatePath)) {
                throw new \Exception('Template báo cáo không tồn tại.');
            }

            // Khởi tạo TemplateProcessor với file mẫu
            $templateProcessor = new TemplateProcessor($templatePath);
            
            // Lấy các tham số từ request
            $startDateInput = $request->input('startDate');
            $endDateInput = $request->input('endDate');
            $donViBaoCao = $request->input('don_vi_bao_cao', '................'); // Giá trị mặc định
            $maDonViBaoCao = $request->input('ma_don_vi_bao_cao', '................');
            $diaChiDonVi = $request->input('dia_chi_don_vi', '................');
            $loaiHinhDonViCode = $request->input('loai_hinh_don_vi');
            // Lấy thông tin người lập báo cáo
            $nguoiLapTen = $request->input('nguoi_lap_ten', '................');
            $nguoiLapSdt = $request->input('nguoi_lap_sdt', '................');
            $nguoiLapEmail = $request->input('nguoi_lap_email', '................');

            // Ánh xạ mã loại hình đơn vị sang tên (tương tự UNIT_TYPES_MAP ở frontend)
            $unitTypesMap = [
                'state' => 'Cơ quan nhà nước',
                'public' => 'Đơn vị sự nghiệp công lập',
                'enterprise' => 'Doanh nghiệp nhà nước'
            ];
            $tenLoaiHinhDonVi = $unitTypesMap[$loaiHinhDonViCode] ?? '................';

            // Thông tin chung
            $now = Carbon::now();
            $templateProcessor->setValue('ngay_bc', $now->day);
            $templateProcessor->setValue('thang_bc', $now->month);
            $templateProcessor->setValue('nam_bc', $now->year);

            // Điền thông tin đơn vị báo cáo và ngày tháng vào template
            // Đảm bảo các placeholder này tồn tại trong file Word của bạn
            $templateProcessor->setValue('ten_don_vi_bc', $donViBaoCao); // Ví dụ: ${ten_don_vi_bc}
            $templateProcessor->setValue('don_vi_bao_cao_uppercase', mb_strtoupper($donViBaoCao, 'UTF-8'));
            $templateProcessor->setValue('ma_dv_bc', $maDonViBaoCao);       // Ví dụ: ${ma_dv_bc}
            $templateProcessor->setValue('dia_chi_dv', $diaChiDonVi);     // Ví dụ: ${dia_chi_dv}
            $templateProcessor->setValue('loai_hinh_dv', $tenLoaiHinhDonVi); // Ví dụ: ${loai_hinh_dv}

            // Điền thông tin người lập báo cáo
            $templateProcessor->setValue('nguoi_lap_ten', $nguoiLapTen);    // Ví dụ: ${nguoi_lap_ten}
            $templateProcessor->setValue('nguoi_lap_sdt', $nguoiLapSdt);      // Ví dụ: ${nguoi_lap_sdt}
            $templateProcessor->setValue('nguoi_lap_email', $nguoiLapEmail);  // Ví dụ: ${nguoi_lap_email}

            // Điền thông tin tổng hợp
            $totals = $reportData['data']['summary']['totals'];
            $templateProcessor->setValue('tong_so_tai_san', number_format($totals['total_count']));
            $templateProcessor->setValue('tong_nguyen_gia', number_format($totals['total_original_value'], 0, ',', '.'));
            $templateProcessor->setValue('tong_khau_hao', number_format($totals['total_depreciation'], 0, ',', '.'));
            $templateProcessor->setValue('tong_gia_tri_con_lai', number_format($totals['total_remaining_value'], 0, ',', '.'));
            $templateProcessor->setValue('tong_dien_tich', number_format($totals['total_area'], 2, ',', '.'));
            $templateProcessor->setValue('tong_hoat_dong', number_format($totals['total_active'])); // Placeholder mới
            $templateProcessor->setValue('tong_khong_hoat_dong', number_format($totals['total_inactive'])); // Placeholder mới

            // Điền thông tin ngày bắt đầu và kết thúc (nếu có và cần thiết trong template)
            // Ví dụ placeholder: ${ky_bc_tu_ngay} và ${ky_bc_den_ngay}
            if ($startDateInput) {
                $templateProcessor->setValue('ky_bc_tu_ngay', Carbon::parse($startDateInput)->format('d/m/Y'));
            }
            if ($endDateInput) {
                $templateProcessor->setValue('ky_bc_den_ngay', Carbon::parse($endDateInput)->format('d/m/Y'));
            }


            // Dữ liệu chi tiết và tổng hợp theo loại
            $allItems = $reportData['data']['data'];
            $summaryByType = $reportData['data']['summary']['by_type'] ?? [];

            // Cấu hình cho từng loại tài sản (block template)
            // Quan trọng: Thứ tự trong mảng này sẽ quyết định thứ tự các block trong báo cáo
            // Đảm bảo 'loai_tai_san' trong $allItems khớp với key ở đây
            $assetTypesConfig = [
                /* 'Hồ chứa' => [ // Giả sử có loại này, nếu không có dữ liệu, block sẽ bị xóa
                    'block_name' => 'HO_CHUA_BLOCK', // Tên block trong Word: {#HO_CHUA_BLOCK}...{/#HO_CHUA_BLOCK}
                    'prefix' => 'hc_', // Prefix cho placeholder trong bảng: ${hc_stt}, ${hc_ten}, ...
                ],
                'Đập dâng' => [
                    'block_name' => 'DAP_DANG_BLOCK',
                    'prefix' => 'dd_',
                ], */
                'Trạm bơm' => [
                    'block_name' => 'TRAM_BOM_BLOCK',
                    'prefix' => 'tb_',
                ],
                'Cống đập' => [
                    'block_name' => 'CONG_DAP_BLOCK',
                    'prefix' => 'cd_',
                ],
                'Kênh mương' => [
                    'block_name' => 'KENH_MUONG_BLOCK',
                    'prefix' => 'km_',
                ],
            ];

            foreach ($assetTypesConfig as $assetTypeNameKey => $config) {
                // Lọc các items thuộc loại tài sản hiện tại
                // $assetTypeNameKey là key từ $assetTypesConfig (ví dụ: 'Trạm bơm')
                // Nó phải khớp với giá trị của trường 'loai_tai_san' trong $allItems
                $itemsOfType = array_filter($allItems, function ($item) use ($assetTypeNameKey) {
                    return isset($item['loai_tai_san']) && $item['loai_tai_san'] === $assetTypeNameKey;
                });
                $itemsOfType = array_values($itemsOfType); // Reset array keys for STT

                // Sắp xếp itemsOfType để nhóm theo id_quyettoan (các giá trị không rỗng lên trước)
                // và sau đó theo tên để đảm bảo thứ tự ổn định, tương tự logic ở frontend.
                usort($itemsOfType, function ($a, $b) {
                    $idQtA = $a['id_quyettoan'] ?? null;
                    $idQtB = $b['id_quyettoan'] ?? null;
                    $nameA = $a['ten'] ?? '';
                    $nameB = $b['ten'] ?? '';

                    // Kiểm tra id_quyettoan rỗng (null hoặc chuỗi trống)
                    $idQtAIsEmpty = ($idQtA === null || (is_string($idQtA) && trim($idQtA) === ''));
                    $idQtBIsEmpty = ($idQtB === null || (is_string($idQtB) && trim($idQtB) === ''));

                    if ($idQtAIsEmpty && $idQtBIsEmpty) {
                        return strcmp($nameA, $nameB); // Cả hai rỗng, sắp xếp theo tên
                    }
                    if ($idQtAIsEmpty) return 1; // A rỗng, đẩy xuống cuối
                    if ($idQtBIsEmpty) return -1; // B rỗng, đẩy xuống cuối

                    // Cả hai id_quyettoan đều không rỗng, so sánh chúng
                    if ($idQtA < $idQtB) return -1;
                    if ($idQtA > $idQtB) return 1;

                    // id_quyettoan giống nhau, sắp xếp theo tên
                    return strcmp($nameA, $nameB);
                });

                if (count($itemsOfType) > 0) {
                    $tableRows = [];
                    $previousIdQt = null; // Theo dõi id_quyettoan của hàng trước đó

                    foreach ($itemsOfType as $index => $item) {
                        $hoat_dong_mark = '';
                        $khong_hoat_dong_mark = '';
                        $current_tinh_trang = $item['tinh_trang'] ?? 'Đang hoạt động';

                        if (isset($item['tinh_trang']) && mb_strtolower(trim($item['tinh_trang'])) === 'hư hỏng') {
                            $khong_hoat_dong_mark = 'x';
                        } else {
                            $hoat_dong_mark = 'x';
                        }

                        $currentIdQt = $item['id_quyettoan'] ?? null;
                        $currentIdQtIsNotEmpty = ($currentIdQt !== null && (is_string($currentIdQt) ? trim($currentIdQt) !== '' : true));

                        // Mặc định hiển thị giá trị
                        $nguyen_gia_display = isset($item['nguyen_gia']) ? number_format($item['nguyen_gia'], 0, ',', '.') : '0';
                        $khau_hao_display = isset($item['khau_hao_tinh']) ? number_format($item['khau_hao_tinh'], 0, ',', '.') : '0';
                        $gtcl_display = isset($item['gia_tri_con_lai']) ? number_format($item['gia_tri_con_lai'], 0, ',', '.') : '0';

                        // Nếu id_quyettoan hiện tại giống với trước đó và không rỗng, thì không hiển thị giá trị tài chính
                        if ($currentIdQtIsNotEmpty && $currentIdQt === $previousIdQt) {
                            $nguyen_gia_display = '';
                            $khau_hao_display = '';
                            $gtcl_display = '';
                        }
                        $tableRows[] = [
                            $config['prefix'] . 'stt' => $index + 1,
                            $config['prefix'] . 'ten' => $item['ten'] ?? '',
                            $config['prefix'] . 'loai_ct' => $item['loai_cong_trinh'] ?? '', // Đảm bảo key 'loai_cong_trinh' tồn tại trong $item
                            $config['prefix'] . 'quy_mo' => $item['quy_mo'] ?? '',
                            $config['prefix'] . 'nam_sd' => $item['nam_su_dung'] ?? '',
                            $config['prefix'] . 'dt_dat' => isset($item['dien_tich_dat']) ? number_format($item['dien_tich_dat'], 2, ',', '.') : '0,00',
                            $config['prefix'] . 'nguyen_gia' => $nguyen_gia_display,
                            $config['prefix'] . 'khau_hao' => $khau_hao_display,
                            $config['prefix'] . 'gtcl' => $gtcl_display,
                            $config['prefix'] . 'tinh_trang' => $current_tinh_trang, // Giữ lại trường tình trạng văn bản gốc
                            $config['prefix'] . 'ghi_chu' => $item['ghi_chu'] ?? '',
                            $config['prefix'] . 'hoat_dong' => $hoat_dong_mark, // Placeholder cho cột "Hoạt động"
                            $config['prefix'] . 'khong_hoat_dong' => $khong_hoat_dong_mark, // Placeholder cho cột "Không hoạt động"
                        ];
                        // Cập nhật previousIdQt cho lần lặp tiếp theo chỉ khi id_quyettoan hiện tại không rỗng
                        if ($currentIdQtIsNotEmpty) {
                            $previousIdQt = $currentIdQt;
                        } else {
                            $previousIdQt = null; // Reset nếu id_quyettoan hiện tại rỗng
                        }
                    }
                    // Điền dữ liệu vào bảng cho block hiện tại
                    // Placeholder đầu tiên trong hàng mẫu của bảng phải là (ví dụ) ${tb_stt}
                    $templateProcessor->cloneRowAndSetValues($config['prefix'] . 'stt', $tableRows);

                    // Điền thông tin tổng hợp cho block (nếu có)
                    // $assetTypeNameKey phải khớp với key trong $summaryByType
                    if (isset($summaryByType[$assetTypeNameKey])) {
                        $typeSummary = $summaryByType[$assetTypeNameKey];
                        $templateProcessor->setValue(
                            $config['prefix'] . 'sum_count', // Ví dụ: ${tb_sum_count}
                            isset($typeSummary['count']) ? number_format($typeSummary['count']) : '0'
                        );
                        $templateProcessor->setValue(
                            $config['prefix'] . 'sum_nguyen_gia', // Ví dụ: ${tb_sum_nguyen_gia}
                            isset($typeSummary['total_value']) ? number_format($typeSummary['total_value'], 0, ',', '.') : '0'
                        );
                        // Thêm các giá trị tổng hợp khác cho block nếu có placeholder tương ứng
                    } else {
                        // Nếu không có summary riêng, có thể để trống hoặc điền 0
                        $templateProcessor->setValue($config['prefix'] . 'sum_count', '0');
                        $templateProcessor->setValue($config['prefix'] . 'sum_nguyen_gia', '0');
                    }
                } else {
                    // Nếu không có dữ liệu cho loại tài sản này, xóa toàn bộ block
                    $templateProcessor->deleteBlock($config['block_name']);
                }
            }

            // Tạo tên file tải về
            $filename = 'BaoCaoKekhaiTSKCHTTHL_' . $now->format('Y-m-d_His') . '.docx'; // Thêm giờ phút giây để tránh trùng
            $tempPath = storage_path('app/temp/' . $filename);
            
            // Đảm bảo thư mục temp tồn tại
            if (!file_exists(storage_path('app/temp'))) {
                mkdir(storage_path('app/temp'), 0755, true);
            }

            $templateProcessor->saveAs($tempPath);

            // Trả về file để download
            return response()->download($tempPath, $filename, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"'
            ])->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            Log::error("Lỗi xuất báo cáo B1A: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json([
                'message' => 'Lỗi khi xuất báo cáo: ' . $e->getMessage()
            ], 500);
        }
    }
}
