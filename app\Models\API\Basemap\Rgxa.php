<?php

namespace App\Models\API\Basemap;

use App\Models\API\Taisan\Congdap;
use Illuminate\Database\Eloquent\Model;

class Rgxa extends Model
{
    protected $connection = 'pgsql'; // <<< Hoặc connection mặc định của bạn
    protected $table = 'basemap.rg_xa'; // <<< Tên bảng chính xác
    protected $primaryKey = 'id';
    public $incrementing = false;
    public $timestamps = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'geom',
        'tenxa',
        'id_huyen',
        'tenhuyen',
        'dientich',
        'shape_leng',
        'shape_area'
    ];

    public function congdaps()
    {
        return $this->hasMany(Congdap::class, 'id_xa', 'id');
    }
}
