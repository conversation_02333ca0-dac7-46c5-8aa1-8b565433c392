<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SpatialQueryTest extends TestCase
{
    /**
     * Test spatial query API endpoint with valid coordinates
     */
    public function test_find_xa_by_coordinates_with_valid_data(): void
    {
        $response = $this->postJson('/api/spatial/find-xa-by-coordinates', [
            'longitude' => 106.7009, // HCMC coordinates
            'latitude' => 10.7769
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'id_xa',
                         'ten_xa',
                         'id_huyen',
                         'ten_huyen',
                         'coordinates' => [
                             'longitude',
                             'latitude'
                         ]
                     ]
                 ]);
    }

    /**
     * Test spatial query API endpoint with invalid coordinates
     */
    public function test_find_xa_by_coordinates_with_invalid_data(): void
    {
        $response = $this->postJson('/api/spatial/find-xa-by-coordinates', [
            'longitude' => 'invalid',
            'latitude' => 10.7769
        ]);

        $response->assertStatus(400)
                 ->assertJsonStructure([
                     'success',
                     'message'
                 ]);
    }

    /**
     * Test spatial query API endpoint with missing coordinates
     */
    public function test_find_xa_by_coordinates_with_missing_data(): void
    {
        $response = $this->postJson('/api/spatial/find-xa-by-coordinates', [
            'longitude' => 106.7009
            // missing latitude
        ]);

        $response->assertStatus(400)
                 ->assertJsonStructure([
                     'success',
                     'message'
                 ]);
    }

    /**
     * Test spatial query API endpoint with coordinates outside Vietnam
     */
    public function test_find_xa_by_coordinates_outside_vietnam(): void
    {
        $response = $this->postJson('/api/spatial/find-xa-by-coordinates', [
            'longitude' => 0, // Greenwich
            'latitude' => 0   // Equator
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'data' => null,
                     'message' => 'Không tìm thấy xã tại tọa độ này'
                 ]);
    }
}
