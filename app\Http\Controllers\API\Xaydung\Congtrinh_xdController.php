<?php

namespace App\Http\Controllers\API\Xaydung;

use App\Http\Controllers\Controller;
use App\Http\Requests\Xaydung\StoreCongtrinh_xdRequest;
use App\Http\Requests\Xaydung\StoreCongtrinh_xdImportRequest;
use App\Http\Requests\Xaydung\UpdateCongtrinh_xdRequest;
use App\Http\Resources\Xaydung\Congtrinh_xdResource;
use App\Http\Resources\xaydung\Congtrinh_xdGeometryResource;
use App\Http\Resources\Xaydung\Congtrinh_xdAttributesResource;
use App\Http\Resources\Xaydung\Congtrinh_xdImportResource;
use App\Services\Congtrinh_xdService;
use App\Services\Congtrinh_xdImportService;
use App\Traits\ApiResponseTrait;
use App\Exceptions\Xaydung\Congtrinh_xdNotFoundException;
use App\Exceptions\Xaydung\InvalidGeometryException;
use App\Exceptions\Xaydung\InvalidExcelFormatException;
use App\Exceptions\Xaydung\ImportValidationException;
use App\Exceptions\Xaydung\BatchProcessingException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;
use Illuminate\Support\Facades\Gate;

class Congtrinh_xdController extends Controller
{
    use ApiResponseTrait;

    protected $congtrinh_xdService;
    protected $congtrinh_xdImportService;

    public function __construct(Congtrinh_xdService $congtrinh_xdService, Congtrinh_xdImportService $congtrinh_xdImportService)
    {
        $this->congtrinh_xdService = $congtrinh_xdService;
        $this->congtrinh_xdImportService = $congtrinh_xdImportService;
        // --- Áp dụng Middleware để kiểm tra quyền CHUNG ---
        // Cách này hiệu quả cho việc kiểm tra "User có quyền thực hiện hành động này nói chung không?"

        /* // Quyền xem (index, show, geometry, attributes)
        $this->middleware('permission:view_data')->only(['index', 'show', 'geometry', 'attributes']);

        // Quyền tạo (store)
        $this->middleware('permission:create_data')->only(['store']);

        // Quyền sửa (update)
        $this->middleware('permission:edit_data')->only(['update']);

        // Quyền xóa (destroy)
        $this->middleware('permission:delete_data')->only(['destroy']);
 */
        /*
        // --- Chuẩn bị cho Bước 2: Sử dụng Policies (Khi cần kiểm tra trên đối tượng cụ thể) ---
        // Khi bạn cần kiểm tra quyền trên từng Congtrinh_xd cụ thể, bạn sẽ:
        // 1. Tạo một Policy: `php artisan make:policy Congtrinh_xdPolicy --model=Taisan\\Congtrinh_xd`
        // 2. Đăng ký Policy trong `AuthServiceProvider`.
        // 3. Thay thế hoặc bổ sung middleware bằng cách gọi `$this->authorize()` trong các phương thức controller.

        // Ví dụ (sẽ dùng sau này khi có Policy):
        // $this->authorizeResource(Congtrinh_xd::class, 'congtrinh_xd'); // Áp dụng policy cho các action chuẩn
        // Hoặc gọi trong từng phương thức:
        // public function show(Congtrinh_xd $congtrinh_xd) { // Sử dụng Route Model Binding
        //     $this->authorize('view', $congtrinh_xd); // Kiểm tra quyền 'view' trên $congtrinh_xd cụ thể
        //     // ...
        // }
        // public function update(UpdateCongtrinh_xdRequest $request, Congtrinh_xd $congtrinh_xd) {
        //     $this->authorize('update', $congtrinh_xd); // Kiểm tra quyền 'update' trên $congtrinh_xd cụ thể
        //     // ...
        // }
        */
    }

    public function index()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congtrinh_xdService->getList($filters, $perPage);

            return $this->paginatedGeoJsonResponse(
                paginator: $paginator,
                resource: Congtrinh_xdResource::class,
                message: 'Lấy danh sách công trình xây dựng thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congtrinh_xd list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách công trình xây dựng', 500);
        }
    }

    public function store(StoreCongtrinh_xdRequest $request)
    {
        try {
            $congtrinh_xd = $this->congtrinh_xdService->create($request->validated());

            // Xóa cache có chủ đích sau khi tạo thành công
            $this->clearCongtrinh_xdCache();

            return $this->createdResponse(
                data: new Congtrinh_xdResource($congtrinh_xd),
                message: 'Tạo công trình xây dựng thành công'
            );

        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error creating Congtrinh_xd: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo công trình xây dựng', 500);
        }
    }

    public function show(string $id)
    {
        try {
            $congtrinh_xd = $this->congtrinh_xdService->getById($id);

            return $this->successResponse(
                data: new Congtrinh_xdResource($congtrinh_xd),
                message: 'Lấy thông tin công trình xây dựng thành công'
            );

        } catch (Congtrinh_xdNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching Congtrinh_xd ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin công trình xây dựng', 500);
        }
    }

    public function update(UpdateCongtrinh_xdRequest $request, string $id)
    {
        try {
            $congtrinh_xd = $this->congtrinh_xdService->update($id, $request->validated());

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearCongtrinh_xdCache();

            return $this->updatedResponse(
                data: new Congtrinh_xdResource($congtrinh_xd),
                message: 'Cập nhật công trình xây dựng thành công'
            );

        } catch (Congtrinh_xdNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (InvalidGeometryException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (Throwable $e) {
            Log::error('Error updating Congtrinh_xd ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật công trình xây dựng', 500);
        }
    }

    public function destroy(string $id)
    {
        try {
            $this->congtrinh_xdService->delete($id);

            // Xóa cache có chủ đích sau khi xóa thành công
            $this->clearCongtrinh_xdCache();

            return $this->deletedResponse('Xóa công trình xây dựng thành công');

        } catch (Congtrinh_xdNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error deleting Congtrinh_xd ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi xóa công trình xây dựng', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm geometry (không có thuộc tính)
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function geometry()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $paginator = $this->congtrinh_xdService->getGeometryList($filters);

            return response()->json([
                'success' => true,
                'message' => 'Lấy dữ liệu geometry công trình xây dựng thành công',
                'data' => [
                    'type' => 'FeatureCollection',
                    'features' => Congtrinh_xdGeometryResource::collection($paginator->items())
                ]
            ]);

        } catch (Throwable $e) {
            Log::error('Error fetching Congtrinh_xd geometry: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu geometry công trình xây dựng', 500);
        }
    }

    /**
     * Lấy danh sách chỉ bao gồm thuộc tính (không có geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function attributes()
    {
        try {
            $filters = [
                'id_xa' => request('id_xa'),
                'loai_ct' => request('loai_ct'),
                'search' => request('search')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->congtrinh_xdService->getAttributesList($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $paginator,
                resource: Congtrinh_xdAttributesResource::class,
                message: 'Lấy dữ liệu thuộc tính công trình xây dựng thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching Congtrinh_xd attributes: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy dữ liệu thuộc tính công trình xây dựng', 500);
        }
    }

    /**
     * Import Excel data into congtrinh_xd table
     */
    public function import(StoreCongtrinh_xdImportRequest $request)
    {
        try {
            $file = $request->file('file');
            $options = [
                'batch_size' => $request->input('batch_size', 100),
                'skip_errors' => $request->input('skip_errors', false),
            ];

            // Perform the import
            $importResult = $this->congtrinh_xdImportService->import($file, $options);

            // Clear cache after successful import
            if ($importResult['successful_imports'] > 0) {
                $this->clearCongtrinh_xdCache();
            }

            // Return formatted response using resource
            return response()->json(
                (new Congtrinh_xdImportResource($importResult))->toArray($request),
                ($importResult['successful_imports'] > 0 && $importResult['failed_imports'] === 0) ? 200 : 207 // 200 cho thành công hoàn toàn, 207 cho thành công một phần hoặc thất bại
            );

        } catch (InvalidExcelFormatException $e) {
            Log::error('Excel format error during import: ' . $e->getMessage());
            return $e->render();
        } catch (ImportValidationException $e) {
            Log::error('Validation error during import: ' . $e->getMessage());
            return $e->render();
        } catch (BatchProcessingException $e) {
            Log::error('Batch processing error during import: ' . $e->getMessage());
            return $e->render();
        } catch (Throwable $e) {
            Log::error('Unexpected error during Excel import: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName() ?? 'unknown',
                'exception' => $e
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi không mong muốn xảy ra trong quá trình import. Vui lòng thử lại.',
                'code' => 'IMPORT_UNEXPECTED_ERROR',
                'data' => [
                    'errors' => [
                        [
                            'row' => 0,
                            'error' => 'Lỗi hệ thống: ' . $e->getMessage()
                        ]
                    ]
                ]
            ], 500);
        }
    }

    /**
     * Xóa cache có chủ đích cho dữ liệu công trình xây dựng
     */
    protected function clearCongtrinh_xdCache(): void
    {
        $cacheTag = 'congtrinh_xd-list';

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: tăng cache version để invalidate tất cả cache
            $versionKey = 'congtrinh_xd_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
