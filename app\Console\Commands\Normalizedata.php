<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class Normalizedata extends Command
{
    protected $signature = 'normalize:data';
    protected $description = 'Chuẩn hoá dữ liệu cột (Unicode, khoảng trắng, chữ thường)';

    public function handle()
    {
        $this->info('Normalize data command - disabled for backend-only setup');
        $this->info('This command requires database configuration and specific tables.');
        $this->info('Please configure your database connection and update this command as needed.');

        return 0;
    }
}
