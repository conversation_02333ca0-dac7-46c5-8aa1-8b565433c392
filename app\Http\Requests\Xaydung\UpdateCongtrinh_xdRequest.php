<?php

namespace App\Http\Requests\Xaydung;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCongtrinh_xdRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type'                => 'sometimes|string|in:Feature',
            'properties'          => 'sometimes|array', // 'sometimes' vì có thể chỉ cập nhật geometry
            'properties.stt_thua'    => 'nullable|string|max:100',
            'properties.id_xa'    => 'nullable|string|max:5',
            'properties.sh_tobando' => 'nullable|string|max:50',
            'properties.nam_capnhat'   => 'nullable|integer',
            'properties.dientich_m2'   => 'nullable|numeric',
            'geometry'            => ['nullable', 'array', function ($attribute, $value, $fail) {
                if ($value && (!isset($value['type']) || !isset($value['coordinates']))) {
                    $fail('The geometry must be a valid GeoJSON object with type and coordinates.');
                }
            }]
        ];
    }

    public function messages(): array
    {
        return [
            'properties.array'    => 'Trường properties phải là một object.',
            'geometry.array'      => 'Geometry phải là một GeoJSON object hợp lệ.',
            // Thêm messages cho các trường con của properties nếu cần
            'properties.stt_thua.string' => 'Số thứ tự thửa phải là chuỗi không quá 100 ký tự.',
            'properties.sh_tobando.string' => 'Số hiệu tờ bản đồ phải là chuỗi không quá 50 ký tự.',
            'properties.dientich_m2.integer' => 'Năm xây dựng phải là số dương.',
        ];
    }
}
