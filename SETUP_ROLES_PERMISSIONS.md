# Hướng dẫn cập nhật hệ thống Roles & Permissions

## 1. Chạy Migration để thêm các trường mới

```bash
php artisan migrate
```

Migration này sẽ thêm các trường:
- `vi_name` và `group` cho bảng `permissions`
- `vi_name`, `description`, và `is_custom` cho bảng `roles`
- `id_xa` cho bảng `users` (nếu chưa có)

## 2. Chạy Seeder để tạo dữ liệu mới

```bash
php artisan db:seed --class=RolePermissionSeeder
```

Seeder này sẽ:
- Xóa toàn bộ dữ liệu cũ (users, roles, permissions)
- Tạo permissions mới với nhóm và tên tiếng Việt
- Tạo roles mới với mô tả và hệ thống phân quyền linh hoạt
- Tạo users mẫu với roles và permissions kết hợp

## 3. <PERSON><PERSON><PERSON> trúc Permissions mới

### Nhóm Permissions:
- **data**: Quản lý Dữ liệu (view_data, create_data, edit_data, delete_data, import_data, export_data)
- **report**: Báo cáo (create_reports, export_reports)
- **user**: Quản lý Người dùng (view_users, create_users, edit_users, delete_users, assign_roles)
- **system**: Hệ thống (view_logs, backup_restore)
- **calculation**: Tính toán (calculate_wear, advanced_calculation)
- **profile**: Hồ sơ (update_profile)

## 4. Cấu trúc Roles mới

### Base Roles:
1. **super_admin**: Siêu quản trị - Toàn quyền hệ thống
2. **admin**: Quản trị viên - Quản lý người dùng và dữ liệu
3. **data_specialist**: Chuyên viên Dữ liệu - Chuyên về quản lý dữ liệu
4. **report_specialist**: Chuyên viên Báo cáo - Chuyên về tạo và quản lý báo cáo
5. **user_manager**: Quản lý Người dùng - Quản lý tài khoản người dùng
6. **viewer**: Người xem - Chỉ xem dữ liệu và báo cáo

## 5. Hệ thống Permissions linh hoạt

Người dùng có thể có:
- **Role chính**: Vai trò cơ bản với bộ permissions mặc định
- **Permissions bổ sung**: Thêm permissions từ các role khác để tạo quyền linh hoạt

Ví dụ: Một user có role `data_specialist` nhưng được thêm permissions `create_reports`, `view_users` để có thêm khả năng tạo báo cáo và xem người dùng.

## 6. Users mẫu được tạo

1. **Admin**: <EMAIL> / 123
   - Role: admin
   - Permissions: Theo role admin

2. **Specialist**: <EMAIL> / 123
   - Role: data_specialist
   - Extra permissions: create_reports, export_reports, view_users, create_users

3. **Viewer**: <EMAIL> / 123
   - Role: viewer
   - Permissions: Chỉ xem dữ liệu

## 7. Giao diện Modal mới

### AddUserModal:
- Chọn vai trò chính
- Chọn quyền bổ sung theo nhóm
- Hiển thị mô tả vai trò

### EditUserModal:
- Chỉnh sửa vai trò chính
- Quản lý quyền bổ sung
- Hiển thị quyền hiện tại

### DeleteUserModal:
- Hiển thị vai trò và mô tả
- Hiển thị quyền bổ sung
- Cảnh báo xóa vĩnh viễn

### UsersTab:
- Hiển thị vai trò với mô tả
- Hiển thị số lượng quyền bổ sung
- Tìm kiếm và lọc theo vai trò

## 8. API Endpoints mới

- `GET /api/roles` - Lấy danh sách roles
- `GET /api/roles/{id}` - Chi tiết role
- `GET /api/permissions` - Lấy danh sách permissions
- `GET /api/permissions/{id}` - Chi tiết permission

## 9. Lưu ý quan trọng

- Backup database trước khi chạy seeder vì nó sẽ xóa toàn bộ dữ liệu users hiện tại
- Tokens sẽ được in ra console và log file
- Hệ thống hỗ trợ cả role-based và permission-based authorization
- Permissions bổ sung sẽ được kết hợp với permissions từ role chính
