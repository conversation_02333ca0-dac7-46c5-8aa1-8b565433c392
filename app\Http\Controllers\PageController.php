<?php

namespace App\Http\Controllers;

class PageController extends Controller
{
    public function map()
    {
        return response()->json([
            'message' => 'Map endpoint - convert to API',
            'data' => []
        ]);
    }

    public function dataManagement()
    {
        return response()->json([
            'message' => 'Data Management endpoint - convert to API',
            'data' => []
        ]);
    }

    public function reports()
    {
        return response()->json([
            'message' => 'Reports endpoint - convert to API',
            'data' => []
        ]);
    }

    public function admin()
    {
        return response()->json([
            'message' => 'Admin endpoint - convert to API',
            'data' => []
        ]);
    }
}