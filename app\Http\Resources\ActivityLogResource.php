<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'time' => $this->created_at->format('d/m/Y H:i:s'),
            'user' => $this->user ? $this->user->name : '<PERSON><PERSON> thống',
            'user_email' => $this->user ? $this->user->email : null,
            'action' => $this->action_name,
            'action_raw' => $this->action,
            'description' => $this->description,
            'ip' => $this->ip_address ?? 'Không xác định',
            'device' => $this->device_info,
            'model_type' => $this->model_type,
            'model_id' => $this->model_id,
            'old_values' => $this->old_values,
            'new_values' => $this->new_values,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
