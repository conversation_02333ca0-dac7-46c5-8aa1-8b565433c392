<?php

namespace App\Http\Resources\Xaydung;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Congtrinh_xdImportErrorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * This resource is used for individual import errors
     */
    public function toArray(Request $request): array
    {
        return [
            'row_number' => $this->resource['row'] ?? 0,
            'error_message' => $this->resource['error'] ?? 'Lỗi không xác định',
            'error_type' => $this->categorizeError($this->resource['error'] ?? ''),
            'severity' => $this->determineSeverity($this->resource['error'] ?? ''),
            'field_data' => $this->sanitizeFieldData($this->resource['data'] ?? []),
            'suggestions' => $this->generateSuggestions($this->resource['error'] ?? ''),
        ];
    }

    /**
     * Categorize error type based on error message
     */
    protected function categorizeError(string $errorMessage): string
    {
        $errorMessage = strtolower($errorMessage);

        if (strpos($errorMessage, 'bắt buộc') !== false) {
            return 'required_field';
        } elseif (strpos($errorMessage, 'vượt quá') !== false || strpos($errorMessage, 'ký tự') !== false) {
            return 'length_validation';
        } elseif (strpos($errorMessage, 'năm') !== false) {
            return 'year_validation';
        } elseif (strpos($errorMessage, 'số') !== false) {
            return 'numeric_validation';
        } elseif (strpos($errorMessage, 'tọa độ') !== false || strpos($errorMessage, 'độ') !== false) {
            return 'coordinate_validation';
        } elseif (strpos($errorMessage, 'định dạng') !== false) {
            return 'format_validation';
        } elseif (strpos($errorMessage, 'trùng') !== false || strpos($errorMessage, 'duplicate') !== false) {
            return 'duplicate_data';
        } else {
            return 'general_error';
        }
    }

    /**
     * Determine error severity
     */
    protected function determineSeverity(string $errorMessage): string
    {
        $errorMessage = strtolower($errorMessage);

        // Critical errors that prevent data import
        if (strpos($errorMessage, 'bắt buộc') !== false) {
            return 'critical';
        }

        // High severity errors
        if (strpos($errorMessage, 'định dạng') !== false || 
            strpos($errorMessage, 'không hợp lệ') !== false) {
            return 'high';
        }

        // Medium severity errors
        if (strpos($errorMessage, 'vượt quá') !== false || 
            strpos($errorMessage, 'khoảng') !== false) {
            return 'medium';
        }

        // Low severity errors (warnings)
        return 'low';
    }

    /**
     * Sanitize field data for safe output
     */
    protected function sanitizeFieldData(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            // Skip internal fields
            if (strpos($key, '_') === 0) {
                continue;
            }

            // Limit string length for display
            if (is_string($value) && strlen($value) > 100) {
                $value = substr($value, 0, 97) . '...';
            }

            // Convert to string for consistent display
            $sanitized[$key] = $value !== null ? (string) $value : null;
        }

        return $sanitized;
    }

    /**
     * Generate suggestions based on error type
     */
    protected function generateSuggestions(string $errorMessage): array
    {
        $suggestions = [];
        $errorType = $this->categorizeError($errorMessage);

        switch ($errorType) {
            case 'required_field':
                $suggestions[] = 'Điền đầy đủ thông tin vào các trường bắt buộc.';
                $suggestions[] = 'Kiểm tra xem có ô nào bị trống không.';
                break;

            case 'length_validation':
                $suggestions[] = 'Rút gọn nội dung để không vượt quá giới hạn ký tự.';
                $suggestions[] = 'Sử dụng từ viết tắt hoặc mô tả ngắn gọn hơn.';
                break;

            case 'year_validation':
                $suggestions[] = 'Nhập năm dưới dạng số nguyên (ví dụ: 2023).';
                $suggestions[] = 'Kiểm tra năm có nằm trong khoảng hợp lệ không.';
                break;

            case 'numeric_validation':
                $suggestions[] = 'Nhập số dương, sử dụng dấu chấm (.) cho số thập phân.';
                $suggestions[] = 'Loại bỏ các ký tự không phải số.';
                break;

            case 'coordinate_validation':
                $suggestions[] = 'Kiểm tra tọa độ: vĩ độ (-90 đến 90), kinh độ (-180 đến 180).';
                $suggestions[] = 'Sử dụng định dạng số thập phân (ví dụ: 10.762622).';
                break;

            case 'format_validation':
                $suggestions[] = 'Kiểm tra định dạng dữ liệu theo mẫu chuẩn.';
                $suggestions[] = 'Tham khảo file mẫu để biết định dạng chính xác.';
                break;

            case 'duplicate_data':
                $suggestions[] = 'Kiểm tra và loại bỏ dữ liệu trùng lặp.';
                $suggestions[] = 'Đảm bảo mỗi bản ghi có thông tin duy nhất.';
                break;

            default:
                $suggestions[] = 'Kiểm tra lại dữ liệu và định dạng file.';
                $suggestions[] = 'Liên hệ quản trị viên nếu vấn đề vẫn tiếp tục.';
                break;
        }

        return $suggestions;
    }
}
