<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🗑️ Cleaning old data...');
        
        // Tắt foreign key constraints tạm thời (PostgreSQL)
        DB::statement('SET session_replication_role = replica;');
        
        // Xóa tất cả relationships trước
        DB::table('model_has_permissions')->truncate();
        DB::table('model_has_roles')->truncate(); 
        DB::table('role_has_permissions')->truncate();
        
        // Xóa personal access tokens
        DB::table('personal_access_tokens')->truncate();
        
        // Xóa users, roles và permissions
        User::truncate();
        Role::truncate();
        Permission::truncate();
        
        // Bật lại foreign key constraints
        DB::statement('SET session_replication_role = DEFAULT;');
        
        $this->command->info('✅ Old data cleaned successfully.');
        $this->command->info('🚀 Creating new permissions, roles and users...');

        // 1. Tạo permissions (đã được đơn giản hóa)
        $permissions = [
            // Quyền Dữ liệu
            ['name' => 'view', 'vi_name' => 'Xem dữ liệu', 'group' => 'data'],
            ['name' => 'edit', 'vi_name' => 'Quản lý dữ liệu', 'group' => 'data'], // Gộp (thêm, sửa, xóa, import, export)
            // Quyền Người dùng
            ['name' => 'administration', 'vi_name' => 'Quản trị hệ thống', 'group' => 'user'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']], 
                [
                    'vi_name' => $permission['vi_name'],
                    'group' => $permission['group']
                ]
            );
        }

        // 2. Tạo Roles (đã được đơn giản hóa)
        $roles = [
            [
                'name' => 'admin',
                'vi_name' => 'Quản trị viên',
                'description' => 'Toàn quyền quản lý dữ liệu và người dùng.',
                'permissions' => ['view', 'edit', 'administration']
            ],
            [
                'name' => 'editor',
                'vi_name' => 'Biên tập viên',
                'description' => 'Quyền xem và chỉnh sửa dữ liệu.',
                'permissions' => ['view', 'edit']
            ],
            [
                'name' => 'viewer',
                'vi_name' => 'Người xem',
                'description' => 'Chỉ có quyền xem dữ liệu.',
                'permissions' => ['view']
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']], 
                [
                    'vi_name' => $roleData['vi_name'],
                    'description' => $roleData['description'] ?? null
                ]
            );
            $role->syncPermissions($roleData['permissions']);
        }

        // 3. Tạo users mẫu
        $this->command->info('Creating sample users...');

        // Admin User
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Quản trị viên',
                'password' => Hash::make('123'),
            ]
        );
        $adminUser->assignRole('admin');
        $adminToken = $adminUser->createToken('admin-seed-token')->plainTextToken;
        Log::info("Admin Token (<EMAIL>): " . $adminToken);
        $this->command->line("Admin Token (<EMAIL>): <fg=yellow>$adminToken</>");

        // Editor User
        $editorUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Biên tập viên',
                'password' => Hash::make('123'),
            ]
        );
        $editorUser->assignRole('editor');
        
        $editorToken = $editorUser->createToken('editor-seed-token')->plainTextToken;
        Log::info("Editor Token (<EMAIL>): " . $editorToken);
        $this->command->line("Editor Token (<EMAIL>): <fg=yellow>$editorToken</>");

        // Viewer User
        $viewerUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Người dùng',
                'password' => Hash::make('123'),
            ]
        );
        $viewerUser->assignRole('viewer');
        
        $viewerToken = $viewerUser->createToken('viewer-seed-token')->plainTextToken;
        Log::info("Viewer Token (<EMAIL>): " . $viewerToken);
        $this->command->line("Viewer Token (<EMAIL>): <fg=yellow>$viewerToken</>");

        $this->command->info('✅ Users and simplified permissions created successfully.');
    }
}