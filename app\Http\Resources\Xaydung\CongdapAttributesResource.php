<?php

namespace App\Http\Resources\Xaydung;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Congtrinh_xdAttributesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'stt_thua' => $this->stt_thua,
            'id_xa' => $this->id_xa,
            /* 'tenxa' => $this->whenLoaded('xa', $this->xa->tenxa ?? null),
            'nguyengia' => $this->whenLoaded('quyettoan', $this->quyettoan->nguyengia ?? null), */
            'sh_tobando' => $this->sh_tobando,
            'dientich_m2' => (float) $this->dientich_m2,
            'nam_capnhat' => $this->nam_capnhat
        ];
    }
}
