<?php

namespace App\Exceptions\Xaydung;

use Exception;

class Congtrinh_xdNotFoundException extends Exception
{
    public function __construct(string $id)
    {
        parent::__construct("Không tìm thấy trông trình xây dựng với ID: {$id}");
    }

    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'code' => 'CONGDAP_NOT_FOUND'
        ], 404);
    }
}