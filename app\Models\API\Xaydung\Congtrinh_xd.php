<?php

namespace App\Models\API\Xaydung;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\API\Basemap\Rgxa;


class Congtrinh_xd extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'xaydung.congtrinh_xd';
    protected $primaryKey = 'id';
    public $timestamps = false;
    public $incrementing = false; // Khóa chính không tự tăng
    protected $keyType = 'string'; // Kiểu dữ liệu khóa chính là chuỗi
    protected $fillable = [
        'stt_thua',
        'id_xa',
        'sh_tobando',
        'dientich_m2',
        'nam_capnhat',
        'geom',
    ];

    // --- THÊM PHẦN TỰ ĐỘNG TẠO ID ---
    protected static function booted()
    {
        static::creating(function ($model) {
            // Chỉ tạo ID nếu nó chưa được gán
            if (empty($model->{$model->getKeyName()})) {
                // Tìm ID lớn nhất hiện có theo dạng '26767.xxxxx'
                // Sử dụng DB facade vì model có thể chưa được khởi tạo hoàn chỉnh
                $latestRecord = DB::connection($model->getConnectionName()) // Chỉ định connection
                    ->table($model->getTable()) // Chỉ định table
                    ->where($model->getKeyName(), 'like', '26767.%')
                    // Sắp xếp theo phần số sau dấu chấm giảm dần
                    ->orderByRaw("CAST(SUBSTRING({$model->getKeyName()} from 7) AS INTEGER) DESC")
                    ->lockForUpdate() // Khóa để tránh race condition
                    ->first();

                $nextNumericId = 0; // Số bắt đầu từ 0
                if ($latestRecord) {
                    // Lấy phần số từ ID cuối cùng (sau dấu chấm) và tăng lên 1
                    $lastNumericId = (int) substr($latestRecord->{$model->getKeyName()}, 6); // Bỏ qua '26767.'
                    $nextNumericId = $lastNumericId + 1;
                }

                // Định dạng ID mới thành '26767.xxxxx'
                $model->{$model->getKeyName()} = "26767." . $nextNumericId;
            }
        });
    }
    // --- KẾT THÚC PHẦN TỰ ĐỘNG TẠO ID ---

    public static function getGeoJSON()
    {
        $query = DB::connection('pgsql')->table('xaydung.congtrinh_xd')
            ->selectRaw('id, ST_AsGeoJSON(geom) as geom, stt_thua, id_xa, sh_tobando, dientich_m2, nam_capnhat')
            ->get();

        $features = $query->map(function ($item) {
            return [
                'type' => 'Feature',
                'geometry' => json_decode($item->geom),
                'properties' => [
                    'id' => (string) $item->id,
                    'stt_thua' => (string) $item->stt_thua,
                    'id_xa' => (string) $item->id_xa,
                    'sh_tobando' => (string) $item->sh_tobando,
                    'dientich_m2' => (float) $item->dientich_m2,
                    'nam_capnhat' => (int) $item->nam_capnhat
                ],
            ];
        });

        return response()->json([
            'type' => 'FeatureCollection',
            'features' => $features,
        ]);
    }

    public static function findAsGeoJSON(string  $id)
    {
        $item = DB::connection('pgsql')->table('xaydung.congtrinh_xd')
            ->selectRaw('
                id,
                ST_AsGeoJSON(geom) as geom_geojson,
                stt_thua,
                id_xa,
                sh_tobando,
                dientich_m2,
                nam_capnhat
             ')
            ->where('id', $id)
            ->first();

        if (!$item) {
            return null;
        }
        return [
            'type' => 'Feature',
            'geometry' => json_decode($item->geom_geojson),
            'properties' => [
                'id' => (string) $item->id,
                'stt_thua' => (string) $item->stt_thua,
                'id_xa' => (string) $item->id_xa,
                'sh_tobando' => (string) $item->sh_tobando,
                'dientich_m2' => (float) $item->dientich_m2,
                'nam_capnhat' => (int) $item->nam_capnhat
            ],
        ];
    }

    public static function geometryFromGeoJSON(string $geojsonString)
    {
        // Escape dấu nháy đơn cho SQL
        $escaped = str_replace("'", "''", $geojsonString);

        // Trả về duy nhất một expression, không thừa dấu phẩy
        return DB::raw("ST_SetSRID(ST_GeomFromGeoJSON('{$escaped}'), 4326)");
    }

    // --- <<< THÊM: Định nghĩa Relationships >>> ---

    /**
     * Lấy thông tin Xã (rg_xa) liên quan đến Cống đập.
     */
    public function xa()
    {
        // belongsTo(RelatedModel, foreign_key, owner_key)
        // foreign_key: Khóa ngoại trong bảng 'xaydung.congtrinh_xd' (là id_xa)
        // owner_key: Khóa chính trong bảng 'basemap.rg_xa' (là 'id')
        return $this->belongsTo(Rgxa::class, 'id_xa', 'id');
    }

    /**
     * Lấy thông tin Quyết toán (quyettoan) liên quan đến Cống đập.
     */
    public function quyettoan()
    {
        // belongsTo(RelatedModel, foreign_key, owner_key)
        // foreign_key: Khóa ngoại trong bảng 'xaydung.congtrinh_xd' (là stt_thua)
        // owner_key: Khóa chính trong bảng 'xaydung.quyettoan' (là 'id')
        //return $this->belongsTo(Quyettoan::class, 'stt_thua', 'id');
    }
    // --- <<< KẾT THÚC: Định nghĩa Relationships >>> ---
}
