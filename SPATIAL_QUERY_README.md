# Chức năng Spatial Query - Tự động cập nhật thông tin xã

## Tóm tắt
Chức năng này cho phép tự động cập nhật thông tin xã khi người dùng di chuyển marker trên bản đồ trong modal chỉnh sửa tài sản.

## Cách sử dụng nhanh

### 1. Mở modal chỉnh sửa tài sản
- Nhấp vào tài sản trên bản đồ hoặc trong danh sách

### 2. Di chuyển marker
- **Kéo thả:** Nhấp và kéo marker đến vị trí mới
- **Tạo mới:** Sử dụng draw tool để tạo marker mới
- **Chỉnh sửa:** Sử dụng edit tool để di chuyển marker

### 3. Thông tin xã tự động cập nhật
- Trường "ID Xã" và "Tên Xã" sẽ tự động đư<PERSON><PERSON> đ<PERSON>ền
- <PERSON><PERSON><PERSON> trường này là readonly (chỉ đọc)

### 4. <PERSON><PERSON><PERSON> thay đổi
- Nhấn "Cập nhật" để lưu tọa độ và thông tin xã mới

## API Endpoint
```
POST /api/spatial/find-xa-by-coordinates
Content-Type: application/json

{
  "longitude": 106.7009,
  "latitude": 10.7769
}
```

## Response
```json
{
  "success": true,
  "data": {
    "id_xa": "26740",
    "ten_xa": "Phường Bến Nghé",
    "id_huyen": "760",
    "ten_huyen": "Quận 1",
    "coordinates": {
      "longitude": 106.7009,
      "latitude": 10.7769
    }
  },
  "message": "Tìm thấy thông tin xã thành công"
}
```

## Test nhanh
1. Mở file `test-spatial-integration.html` trong browser
2. Nhấn "Test API" để kiểm tra API hoạt động
3. Thử với tọa độ khác nhau

## Files đã thay đổi
- `resources/js/components/modal/EditAssetModal.vue` - Frontend implementation
- `app/Http/Controllers/API/SpatialController.php` - API controller (đã có sẵn)
- `routes/api.php` - API routes (đã có sẵn)
- `docs/spatial-query-feature.md` - Tài liệu chi tiết

## Troubleshooting nhanh

### Marker không kéo được
- Kiểm tra console log có lỗi JavaScript
- Refresh trang và thử lại

### Thông tin xã không cập nhật
- Kiểm tra network tab trong DevTools
- Đảm bảo API endpoint hoạt động

### API không phản hồi
- Kiểm tra Laravel server đang chạy: `php artisan serve`
- Kiểm tra database connection

## Liên hệ
Xem tài liệu chi tiết tại `docs/spatial-query-feature.md` để biết thêm thông tin kỹ thuật.
