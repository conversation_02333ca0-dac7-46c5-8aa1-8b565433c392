<?php

namespace App\Exceptions\User;

use Exception;

class UserNotFoundException extends Exception
{
    public function __construct(int $id)
    {
        parent::__construct("Không tìm thấy người dùng với ID: {$id}");
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'USER_NOT_FOUND'
        ], 404);
    }
}
