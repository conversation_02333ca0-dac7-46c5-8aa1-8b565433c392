<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add custom fields to permissions table
        Schema::table('permissions', function (Blueprint $table) {
            $table->string('vi_name')->nullable()->after('name');
            $table->string('group')->default('general')->after('vi_name');
        });

        // Add custom fields to roles table
        Schema::table('roles', function (Blueprint $table) {
            $table->string('vi_name')->nullable()->after('name');
            $table->text('description')->nullable()->after('vi_name');
            $table->boolean('is_custom')->default(false)->after('description');
        });

        // Add id_xa field to users table if not exists
        if (!Schema::hasColumn('users', 'id_xa')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('id_xa')->nullable()->after('email');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropColumn(['vi_name', 'group']);
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn(['vi_name', 'description', 'is_custom']);
        });

        if (Schema::hasColumn('users', 'id_xa')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('id_xa');
            });
        }
    }
};
