<?php

namespace App\Http\Requests\Role;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRoleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $roleId = $this->route('role') ?? $this->route('id');
        
        return [
            'name' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('roles')->ignore($roleId)
            ],
            'vi_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:500',
            'is_custom' => 'nullable|boolean',
            'permissions' => 'nullable|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ];
    }

    public function messages(): array
    {
        return [
            'name.string' => 'Tên vai trò phải là chuỗi.',
            'name.max' => 'Tên vai trò không được vượt quá 255 ký tự.',
            'name.unique' => 'Tên vai trò này đã tồn tại.',
            'vi_name.string' => 'Tên tiếng Việt phải là chuỗi.',
            'vi_name.max' => 'Tên tiếng Việt không được vượt quá 255 ký tự.',
            'description.string' => 'Mô tả phải là chuỗi.',
            'description.max' => 'Mô tả không được vượt quá 500 ký tự.',
            'is_custom.boolean' => 'Trường tùy chỉnh phải là true hoặc false.',
            'permissions.array' => 'Quyền phải là một mảng.',
            'permissions.*.integer' => 'ID quyền phải là số nguyên.',
            'permissions.*.exists' => 'Quyền không tồn tại.',
        ];
    }
}
