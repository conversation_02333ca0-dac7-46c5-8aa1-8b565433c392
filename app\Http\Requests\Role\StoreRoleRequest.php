<?php

namespace App\Http\Requests\Role;

use Illuminate\Foundation\Http\FormRequest;

class StoreRoleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:roles,name',
            'vi_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'is_custom' => 'nullable|boolean',
            'permissions' => 'nullable|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Tên vai trò là bắt buộc.',
            'name.string' => 'Tên vai trò phải là chuỗi.',
            'name.max' => 'Tên vai trò không được vượt quá 255 ký tự.',
            'name.unique' => 'Tên vai trò này đã tồn tại.',
            'vi_name.required' => 'Tên tiếng Việt là bắt buộc.',
            'vi_name.string' => 'Tên tiếng Việt phải là chuỗi.',
            'vi_name.max' => 'Tên tiếng Việt không được vượt quá 255 ký tự.',
            'description.string' => 'Mô tả phải là chuỗi.',
            'description.max' => 'Mô tả không được vượt quá 500 ký tự.',
            'is_custom.boolean' => 'Trường tùy chỉnh phải là true hoặc false.',
            'permissions.array' => 'Quyền phải là một mảng.',
            'permissions.*.integer' => 'ID quyền phải là số nguyên.',
            'permissions.*.exists' => 'Quyền không tồn tại.',
        ];
    }
}
