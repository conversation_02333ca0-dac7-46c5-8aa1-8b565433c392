# Congdap Excel Import API Documentation

## Overview
The Congdap Import API allows users to import Excel data into the congdap table with comprehensive validation, batch processing, and error handling.

## Endpoint
```
POST /api/taisan/congdap/import
```

## Request Parameters

### Required Parameters
- `file` (file): Excel file (.xlsx or .xls format)
  - Maximum size: 10MB
  - Supported formats: Excel 2007+ (.xlsx), Excel 97-2003 (.xls)

### Optional Parameters
- `batch_size` (integer): Number of records to process in each batch (default: 100, max: 1000)
- `skip_errors` (boolean): Whether to continue processing when errors occur (default: false)

## Excel File Format

### Required Columns
- `ten` or `tên` or `ten_cong_trinh`: Name of the structure (required)

### Optional Columns
- `id_qt` or `ma_quyet_toan`: Decision ID
- `id_xa` or `ma_xa`: Commune ID
- `quymo_ct` or `quy_mo`: Scale of construction
- `loai_ct` or `loai`: Type of construction
- `nam_xd` or `nam_xay_dung`: Construction year
- `nam_sd` or `nam_su_dung`: Usage year
- `dt_dat` or `dien_tich`: Land area
- `tinhtrang` or `tinh_trang`: Status
- `quytrinh_vh`: VH process
- `quytrinh_bt`: BT process
- `dv_quanly` or `don_vi_quan_ly`: Management unit
- `phuongthuc` or `phuong_thuc`: Method
- `chuthich` or `chu_thich`: Notes
- `latitude` or `vi_do`: Latitude coordinate
- `longitude` or `kinh_do`: Longitude coordinate

## Response Format

### Success Response (200/207)
```json
{
  "success": true,
  "message": "Import thành công 95/100 bản ghi.",
  "statistics": {
    "total_rows": 100,
    "processed_rows": 100,
    "successful_imports": 95,
    "failed_imports": 5,
    "success_rate": 95.0
  },
  "performance": {
    "processing_time_ms": 1250.5,
    "memory_usage_mb": 12.3,
    "average_time_per_row": 12.5
  },
  "errors": [
    {
      "row_number": 15,
      "error_message": "Dòng 15: Tên công trình là bắt buộc.",
      "error_type": "required_field",
      "severity": "critical",
      "field_data": {
        "id_qt": "QT001",
        "ten": ""
      },
      "suggestions": [
        "Điền đầy đủ thông tin vào các trường bắt buộc.",
        "Kiểm tra xem có ô nào bị trống không."
      ]
    }
  ],
  "summary": {
    "status": "partial",
    "completion_percentage": 100.0,
    "recommendations": [
      "Kiểm tra và điền đầy đủ các trường bắt buộc (đặc biệt là tên công trình)."
    ]
  }
}
```

### Error Response (422)
```json
{
  "success": false,
  "message": "File phải có định dạng Excel (.xlsx hoặc .xls).",
  "code": "INVALID_EXCEL_FORMAT",
  "data": {
    "errors": [
      {
        "row": 0,
        "error": "File không phải là file Excel hợp lệ."
      }
    ]
  }
}
```

## Status Codes
- `200`: Complete success (all records imported successfully)
- `207`: Partial success (some records imported, some failed)
- `422`: Validation error (invalid file or parameters)
- `500`: Server error

## Features

### 1. Automatic ID Generation
- Records are automatically assigned IDs with 'C' prefix (e.g., C0001, C0002)
- Collision detection and retry logic included

### 2. Flexible Column Mapping
- Supports multiple column name variations (Vietnamese and English)
- Case-insensitive header matching

### 3. Data Validation
- Required field validation
- Data type validation (numbers, years, coordinates)
- Length validation for text fields
- Coordinate range validation

### 4. GeoJSON Support
- Automatic conversion of latitude/longitude to PostGIS geometry
- Point geometry creation from coordinate pairs

### 5. Batch Processing
- Configurable batch sizes for memory efficiency
- Transaction-based processing for data integrity

### 6. Error Handling
- Detailed error reporting with row numbers
- Error categorization and severity levels
- Actionable suggestions for fixing errors

### 7. Performance Monitoring
- Processing time tracking
- Memory usage monitoring
- Performance metrics per row

### 8. Cache Management
- Automatic cache invalidation after successful imports
- Uses 'congdap-list' cache tag for granular control

## Frontend Integration

The API is designed to work seamlessly with the existing `ImportAssetTab.vue` component:

```javascript
const response = await axios.post('/api/taisan/congdap/import', formData, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});

if (response.data.success) {
  // Handle success
  emit('refresh-data');
} else {
  // Handle errors
  console.error(response.data.message);
}
```

## Error Types

- `required_field`: Missing required data
- `length_validation`: Text too long
- `year_validation`: Invalid year format
- `numeric_validation`: Invalid number format
- `coordinate_validation`: Invalid coordinates
- `format_validation`: Invalid data format
- `duplicate_data`: Duplicate records
- `general_error`: Other errors

## Best Practices

1. **File Preparation**
   - Use the provided template file
   - Ensure required fields are filled
   - Validate coordinates before upload

2. **Batch Processing**
   - Use smaller batch sizes for large files
   - Enable error skipping for data cleaning scenarios

3. **Error Handling**
   - Review error messages and suggestions
   - Fix data issues and re-import failed records

4. **Performance**
   - Monitor processing time and memory usage
   - Consider splitting very large files

## Template File
Download the Excel template from: `/api/taisan/congdap/template`
