<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Resources\User\UserResource;
use App\Services\UserService;
use App\Traits\ApiResponseTrait;
use App\Exceptions\User\UserNotFoundException;
use App\Exceptions\User\UserValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Throwable;

class UserController extends Controller
{
    use ApiResponseTrait;

    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;

        // --- Áp dụng Middleware để kiểm tra quyền CHUNG ---
        // Cách này hiệu quả cho việc kiểm tra "User c<PERSON> quyền thực hiện hành động này nói chung không?"

        /* // Quyền xem (index, show)
        $this->middleware('permission:view_users')->only(['index', 'show']);

        // Quyền tạo (store)
        $this->middleware('permission:create_users')->only(['store']);

        // Quyền sửa (update)
        $this->middleware('permission:edit_users')->only(['update']);

        // Quyền xóa (destroy)
        $this->middleware('permission:delete_users')->only(['destroy']);
        */
    }

    public function index()
    {
        try {
            $filters = [
                'search' => request('search'),
                'role' => request('role'),
                'status' => request('status'),
                'email_verified' => request('email_verified'),
                'created_from' => request('created_from'),
                'created_to' => request('created_to')
            ];

            $perPage = request('per_page', 10);

            $paginator = $this->userService->getList($filters, $perPage);

            return $this->paginatedResponse(
                paginator: $paginator,
                resource: UserResource::class,
                message: 'Lấy danh sách người dùng thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching User list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách người dùng', 500);
        }
    }

    public function store(StoreUserRequest $request)
    {
        try {
            $user = $this->userService->create($request->validated());

            // Xóa cache có chủ đích sau khi tạo thành công
            $this->clearUserCache();

            return $this->createdResponse(
                data: new UserResource($user),
                message: 'Tạo người dùng thành công'
            );

        } catch (UserValidationException $e) {
            return $this->errorResponse($e->getMessage(), 422, $e->getErrors());
        } catch (Throwable $e) {
            Log::error('Error creating User: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi tạo người dùng', 500);
        }
    }

    public function show(int $id)
    {
        try {
            $user = $this->userService->getById($id);

            return $this->successResponse(
                data: new UserResource($user),
                message: 'Lấy thông tin người dùng thành công'
            );

        } catch (UserNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error fetching User ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy thông tin người dùng', 500);
        }
    }

    public function update(UpdateUserRequest $request, int $id)
    {
        try {
            $user = $this->userService->update($id, $request->validated());

            // Xóa cache có chủ đích sau khi cập nhật thành công
            $this->clearUserCache();

            return $this->updatedResponse(
                data: new UserResource($user),
                message: 'Cập nhật người dùng thành công'
            );

        } catch (UserNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (UserValidationException $e) {
            return $this->errorResponse($e->getMessage(), 422, $e->getErrors());
        } catch (Throwable $e) {
            Log::error('Error updating User ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi cập nhật người dùng', 500);
        }
    }

    public function destroy(int $id)
    {
        try {
            $this->userService->delete($id);

            // Xóa cache có chủ đích sau khi xóa thành công
            $this->clearUserCache();

            return $this->deletedResponse('Xóa người dùng thành công');

        } catch (UserNotFoundException $e) {
            return $this->errorResponse($e->getMessage(), 404);
        } catch (Throwable $e) {
            Log::error('Error deleting User ID ' . $id . ': ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi xóa người dùng', 500);
        }
    }

    /**
     * Xóa cache có chủ đích cho dữ liệu người dùng
     */
    protected function clearUserCache(): void
    {
        $cacheTag = 'users-list';

        // Kiểm tra xem cache driver có hỗ trợ tags không
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: tăng cache version để invalidate tất cả cache
            $versionKey = 'users_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
