<?php

namespace Database\Seeders;

use App\Models\ActivityLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ActivityLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing users or create some if none exist
        $users = User::all();

        if ($users->isEmpty()) {
            // Create some sample users if none exist
            $users = collect([
                User::create([
                    'name' => 'Nguyễn Văn A',
                    'email' => 'nguyen<PERSON>@example.com',
                    'password' => bcrypt('password'),
                ]),
                User::create([
                    'name' => 'Trần Thị B',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                ]),
                User::create([
                    'name' => 'Lê Văn C',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                ]),
            ]);
        }

        $actions = ['login', 'logout', 'create', 'update', 'delete', 'view', 'export', 'import'];
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/119.0 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
        ];

        $ipAddresses = [
            '*************',
            '*************',
            '*************',
            '*************',
            '*************',
            '*********',
            '*********',
        ];

        $descriptions = [
            'login' => [
                'Đăng nhập thành công vào hệ thống',
                'Đăng nhập vào hệ thống quản lý tài sản',
                'Truy cập hệ thống thành công',
            ],
            'logout' => [
                'Đăng xuất khỏi hệ thống',
                'Kết thúc phiên làm việc',
                'Thoát khỏi hệ thống',
            ],
            'create' => [
                'Thêm mới tài sản "Đập Tuyển Quang" (C-00131)',
                'Tạo mới trạm bơm "Trạm bơm Liên Mạc" (T-00125)',
                'Thêm kênh mương "Kênh Đồng Cửu Long" (K-00129)',
                'Tạo mới người dùng trong hệ thống',
                'Thêm vai trò mới cho hệ thống',
            ],
            'update' => [
                'Cập nhật thông tin tài sản "Đập Tuyển Quang" (C-00131)',
                'Chỉnh sửa thông tin trạm bơm "Trạm bơm Liên Mạc" (T-00125)',
                'Cập nhật dữ liệu kênh mương "Kênh Đồng Cửu Long" (K-00129)',
                'Cập nhật thông tin người dùng',
                'Chỉnh sửa quyền của vai trò',
            ],
            'delete' => [
                'Xóa tài sản "Trạm bơm Thanh Hà" (T-00128)',
                'Xóa kênh mương "Kênh Bắc Hưng Hải" (K-00127)',
                'Xóa người dùng khỏi hệ thống',
                'Xóa vai trò không sử dụng',
            ],
            'view' => [
                'Xem danh sách tài sản cống đập',
                'Truy cập trang quản lý trạm bơm',
                'Xem báo cáo thống kê tài sản',
                'Truy cập trang quản lý người dùng',
            ],
            'export' => [
                'Xuất báo cáo danh mục TSKCHTLL Quý II/2025',
                'Xuất dữ liệu tài sản ra Excel',
                'Tải xuống báo cáo thống kê',
                'Xuất danh sách người dùng',
            ],
            'import' => [
                'Nhập dữ liệu tài sản từ file Excel',
                'Import danh sách cống đập mới',
                'Nhập dữ liệu trạm bơm từ template',
                'Import dữ liệu kênh mương',
            ],
        ];

        // Create 50 sample activity logs
        for ($i = 0; $i < 50; $i++) {
            $user = $users->random();
            $action = $actions[array_rand($actions)];
            $description = $descriptions[$action][array_rand($descriptions[$action])];

            ActivityLog::create([
                'user_id' => $user->id,
                'action' => $action,
                'description' => $description,
                'ip_address' => $ipAddresses[array_rand($ipAddresses)],
                'user_agent' => $userAgents[array_rand($userAgents)],
                'model_type' => $this->getRandomModelType(),
                'model_id' => $this->getRandomModelId(),
                'created_at' => Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                'updated_at' => Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
            ]);
        }
    }

    private function getRandomModelType(): ?string
    {
        $modelTypes = [
            'App\\Models\\API\\Taisan\\Congdap',
            'App\\Models\\API\\Taisan\\Trambom',
            'App\\Models\\API\\Taisan\\Kenhmuong',
            'App\\Models\\User',
            null, // Some activities might not have a specific model
        ];

        return $modelTypes[array_rand($modelTypes)];
    }

    private function getRandomModelId(): ?string
    {
        $modelIds = [
            'C-00131',
            'T-00125',
            'K-00129',
            'T-00128',
            'K-00127',
            '1',
            '2',
            '3',
            null, // Some activities might not have a specific model ID
        ];

        return $modelIds[array_rand($modelIds)];
    }
}
