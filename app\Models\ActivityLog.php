<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActivityLog extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'description',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship với User
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope để lọc theo action
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope để lọc theo user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope để lọc theo khoảng thời gian
     */
    public function scopeByDateRange($query, $from, $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }

    /**
     * Scope để tìm kiếm trong description
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('description', 'like', '%' . $search . '%');
    }

    /**
     * Lấy tên action dễ đọc
     */
    public function getActionNameAttribute()
    {
        $actions = [
            'login' => 'Đăng nhập',
            'logout' => 'Đăng xuất',
            'create' => 'Tạo mới',
            'update' => 'Cập nhật',
            'delete' => 'Xóa',
            'view' => 'Xem',
            'export' => 'Xuất báo cáo',
            'import' => 'Nhập dữ liệu',
            'approve' => 'Phê duyệt',
            'reject' => 'Từ chối',
        ];

        return $actions[$this->action] ?? $this->action;
    }

    /**
     * Lấy thông tin thiết bị từ user agent
     */
    public function getDeviceInfoAttribute()
    {
        if (!$this->user_agent) {
            return 'Không xác định';
        }

        // Phân tích user agent để lấy thông tin browser và OS
        $userAgent = $this->user_agent;

        // Detect browser
        $browser = 'Unknown';
        if (strpos($userAgent, 'Chrome') !== false) {
            preg_match('/Chrome\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Chrome ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            preg_match('/Firefox\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Firefox ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Safari') !== false && strpos($userAgent, 'Chrome') === false) {
            preg_match('/Version\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Safari ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'Edge') !== false) {
            preg_match('/Edge\/([0-9.]+)/', $userAgent, $matches);
            $browser = 'Edge ' . ($matches[1] ?? '');
        }

        // Detect OS
        $os = 'Unknown';
        if (strpos($userAgent, 'Windows NT 10') !== false) {
            $os = 'Windows 10';
        } elseif (strpos($userAgent, 'Windows NT 11') !== false) {
            $os = 'Windows 11';
        } elseif (strpos($userAgent, 'Windows') !== false) {
            $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac OS X') !== false) {
            preg_match('/Mac OS X ([0-9_]+)/', $userAgent, $matches);
            $os = 'macOS ' . str_replace('_', '.', $matches[1] ?? '');
        } elseif (strpos($userAgent, 'Ubuntu') !== false) {
            $os = 'Ubuntu';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $os = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            preg_match('/Android ([0-9.]+)/', $userAgent, $matches);
            $os = 'Android ' . ($matches[1] ?? '');
        } elseif (strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $os = 'iOS';
        }

        return $browser . ' / ' . $os;
    }
}
