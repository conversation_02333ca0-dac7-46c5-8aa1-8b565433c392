<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class PermissionController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display a listing of the permissions.
     */
    public function index()
    {
        try {
            $permissions = Permission::all()->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'vi_name' => $permission->vi_name ?? $permission->name,
                    'group' => $permission->group ?? 'general',
                    'created_at' => $permission->created_at?->toISOString(),
                    'updated_at' => $permission->updated_at?->toISOString(),
                ];
            });

            return $this->successResponse(
                data: $permissions,
                message: '<PERSON><PERSON><PERSON> danh sách quyền thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching permissions list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách quyền', 500);
        }
    }

    /**
     * Display the specified permission.
     */
    public function show(string $id)
    {
        try {
            $permission = Permission::findOrFail($id);

            $permissionData = [
                'id' => $permission->id,
                'name' => $permission->name,
                'vi_name' => $permission->vi_name ?? $permission->name,
                'group' => $permission->group ?? 'general',
                'created_at' => $permission->created_at?->toISOString(),
                'updated_at' => $permission->updated_at?->toISOString(),
            ];

            return $this->successResponse(
                data: $permissionData,
                message: 'Lấy thông tin quyền thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching permission details: ' . $e->getMessage());
            return $this->errorResponse('Không tìm thấy quyền', 404);
        }
    }
}
