<?php

namespace App\Services;

use App\Models\API\Xaydung\Congtrinh_xd;
use App\Repositories\Contracts\Congtrinh_xdRepositoryInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Exception;

class Congtrinh_xdImportService
{
    protected $repository;
    protected $batchSize;
    protected $skipErrors;
    
    // Column mapping from Excel headers to database fields
    protected $columnMapping = [
        'stt_thua' => ['số thứ tự thửa'],
        'id_xa' => ['mã xã'],
        'sh_tobando' => ['số hiệu tờ bản đồ'],
        'dientich_m2' => ['diện tích theo hệ toạ độ phẳng VN2000'],
        'nam_capnhat' => ['năm cập nhật dữ liệu'],
        'latitude' => ['vĩ độ'],
        'longitude' => ['kinh độ'],
    ];

    public function __construct(Congtrinh_xdRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->batchSize = 100; // Default batch size
        $this->skipErrors = false; // Default: don't skip errors
    }

    /**
     * Import Excel file data into Congtrinh_xd table
     */
    public function import(UploadedFile $file, array $options = []): array
    {
        $this->batchSize = $options['batch_size'] ?? $this->batchSize;
        $this->skipErrors = $options['skip_errors'] ?? $this->skipErrors;
        
        $startTime = microtime(true);
        $stats = [
            'total_rows' => 0,
            'processed_rows' => 0,
            'successful_imports' => 0,
            'failed_imports' => 0,
            'errors' => [],
            'processing_time' => 0,
            'memory_usage' => 0,
        ];

        try {
            // Parse Excel file
            $data = $this->parseExcelFile($file);
            $stats['total_rows'] = count($data);

            if (empty($data)) {
                throw new Exception('File Excel không chứa dữ liệu hợp lệ.');
            }

            // Process data in batches
            $batches = array_chunk($data, $this->batchSize);
            
            foreach ($batches as $batchIndex => $batch) {
                $batchResult = $this->processBatch($batch, $batchIndex + 1);
                
                $stats['processed_rows'] += $batchResult['processed'];
                $stats['successful_imports'] += $batchResult['successful'];
                $stats['failed_imports'] += $batchResult['failed'];
                $stats['errors'] = array_merge($stats['errors'], $batchResult['errors']);
                
                // Stop processing if we have critical errors and not skipping
                if (!$this->skipErrors && !empty($batchResult['errors'])) {
                    break;
                }
            }

            // Clear cache after successful import
            if ($stats['successful_imports'] > 0) {
                $this->clearCongtrinh_xdCache();
            }

        } catch (Exception $e) {
            Log::error('Excel import failed: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'exception' => $e
            ]);
            
            $stats['errors'][] = [
                'row' => 0,
                'error' => 'Lỗi xử lý file: ' . $e->getMessage()
            ];
        }

        $stats['processing_time'] = round((microtime(true) - $startTime) * 1000, 2); // milliseconds
        $stats['memory_usage'] = round(memory_get_peak_usage(true) / 1024 / 1024, 2); // MB

        return $stats;
    }

    /**
     * Parse Excel file and extract data
     */
    protected function parseExcelFile(UploadedFile $file): array
    {
        try {
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            if (empty($data)) {
                return [];
            }

            // Get headers from first row
            // Sử dụng mb_strtolower để xử lý đúng tiếng Việt có dấu (UTF-8)
            $headers = array_map(function($header) {
                return trim(mb_strtolower((string)$header, 'UTF-8'));
            }, $data[0]);
            $mappedHeaders = $this->mapHeaders($headers);
            
            // Process data rows (skip header row)
            $processedData = [];
            for ($i = 1; $i < count($data); $i++) {
                $row = $data[$i];
                $mappedRow = [];
                
                foreach ($row as $index => $value) {
                    if (isset($mappedHeaders[$index])) {
                        $mappedRow[$mappedHeaders[$index]] = $this->cleanValue($value);
                    }
                }
                
                // Skip empty rows
                if (!empty(array_filter($mappedRow))) {
                    $mappedRow['_row_number'] = $i + 1; // Store original row number for error reporting
                    $processedData[] = $mappedRow;
                }
            }

            return $processedData;

        } catch (Exception $e) {
            throw new Exception('Không thể đọc file Excel: ' . $e->getMessage());
        }
    }

    /**
     * Map Excel headers to database fields
     */
    protected function mapHeaders(array $headers): array
    {
        $mappedHeaders = [];
        
        foreach ($headers as $index => $header) {
            // Header đã được trim và chuyển thành chữ thường (sử dụng mb_strtolower) từ parseExcelFile
            foreach ($this->columnMapping as $dbField => $possibleHeaders) {
                // So sánh trực tiếp vì các giá trị trong $possibleHeaders đã là chữ thường
                if (in_array($header, $possibleHeaders)) {
                    $mappedHeaders[$index] = $dbField;
                    break;
                }
            }
        }
        
        return $mappedHeaders;
    }

    /**
     * Clean and validate cell value
     */
    protected function cleanValue($value)
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        // Convert to string and trim
        $value = trim((string) $value);
        
        // Return null for empty strings
        return $value === '' ? null : $value;
    }

    /**
     * Process a batch of data
     */
    protected function processBatch(array $batch, int $batchNumber): array
    {
        $result = [
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        DB::beginTransaction();
        
        try {
            foreach ($batch as $rowData) {
                $result['processed']++;
                $rowNumber = $rowData['_row_number'] ?? $result['processed'];
                unset($rowData['_row_number']);
                
                try {
                    $validatedData = $this->validateAndTransformRow($rowData, $rowNumber);
                    $this->createCongtrinh_xdRecord($validatedData);
                    $result['successful']++;
                    
                } catch (Exception $e) {
                    $result['failed']++;
                    $result['errors'][] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $rowData
                    ];
                    
                    if (!$this->skipErrors) {
                        throw $e; // Re-throw to stop batch processing
                    }
                }
            }
            
            DB::commit();
            
        } catch (Exception $e) {
            DB::rollBack();
            
            // If not skipping errors, mark all remaining rows as failed
            if (!$this->skipErrors) {
                $result['failed'] = $result['processed'];
                $result['successful'] = 0;
            }
            
            // throw $e; // Không ném lại ngoại lệ, để phương thức import có thể thu thập số liệu và quyết định dừng hay tiếp tục.
        }

        return $result;
    }

    /**
     * Validate and transform row data
     */
    protected function validateAndTransformRow(array $rowData, int $rowNumber): array
    {
        $validatedData = [];

        // Validate required fields (at least name should be present)
        /* if (empty($rowData['ten'])) {
            throw new Exception("Dòng {$rowNumber}: Tên công trình là bắt buộc.");
        } */

        // Map and validate each field
        foreach ($rowData as $field => $value) {
            switch ($field) {
                case 'stt_thua':
                    $validatedData['stt_thua'] = $this->validateString($value, 100, "Dòng {$rowNumber}: số thứ tự thửa", true);
                    break;

                case 'id_xa':
                    $validatedData['id_xa'] = $this->validateString($value, 5, "Dòng {$rowNumber}: Mã xã");
                    break;

                case 'sh_tobando':
                    $validatedData['sh_tobando'] = $this->validateString($value, 50, "Dòng {$rowNumber}: số hiệu tờ bản đồ");
                    break;

                case 'dientich_m2':
                    $validatedData['dientich_m2'] = $this->validateNumeric($value, "Dòng {$rowNumber}: diện tích theo hệ toạ độ phẳng VN2000");
                    break;

                case 'nam_capnhat':
                    $validatedData['nam_capnhat'] = $this->validateYear($value, "Dòng {$rowNumber}: năm cập nhật dữ liệu");
                    break;

                case 'latitude':
                case 'longitude':
                    // Store coordinates for later GeoJSON conversion
                    $validatedData[$field] = $this->validateCoordinate($value, $field, "Dòng {$rowNumber}");
                    break;
            }
        }

        // Convert coordinates to GeoJSON if both latitude and longitude are present
        if (isset($validatedData['latitude']) && isset($validatedData['longitude'])) {
            $validatedData['geom'] = $this->createPointGeometry(
                $validatedData['longitude'],
                $validatedData['latitude']
            );
            unset($validatedData['latitude'], $validatedData['longitude']);
        }

        return $validatedData;
    }

    /**
     * Validate string field
     */
    protected function validateString($value, int $maxLength, string $fieldName, bool $required = false)
    {
        if ($value === null || $value === '') {
            if ($required) {
                throw new Exception("{$fieldName} là bắt buộc.");
            }
            return null;
        }

        $value = trim((string) $value);

        if (strlen($value) > $maxLength) {
            throw new Exception("{$fieldName} không được vượt quá {$maxLength} ký tự.");
        }

        return $value;
    }

    /**
     * Validate year field
     */
    protected function validateYear($value, string $fieldName)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $year = filter_var($value, FILTER_VALIDATE_INT);

        if ($year === false || $year < 1900 || $year > date('Y') + 10) {
            throw new Exception("{$fieldName} phải là năm hợp lệ (1900 - " . (date('Y') + 10) . ").");
        }

        return $year;
    }

    /**
     * Validate numeric field
     */
    protected function validateNumeric($value, string $fieldName)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $numeric = filter_var($value, FILTER_VALIDATE_FLOAT);

        if ($numeric === false || $numeric < 0) {
            throw new Exception("{$fieldName} phải là số dương.");
        }

        return $numeric;
    }

    /**
     * Validate coordinate field
     */
    protected function validateCoordinate($value, string $type, string $context)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $coordinate = filter_var($value, FILTER_VALIDATE_FLOAT);

        if ($coordinate === false) {
            throw new Exception("{$context}: {$type} phải là số thực.");
        }

        // Validate coordinate ranges
        if ($type === 'latitude' && ($coordinate < -90 || $coordinate > 90)) {
            throw new Exception("{$context}: Vĩ độ phải trong khoảng -90 đến 90.");
        }

        if ($type === 'longitude' && ($coordinate < -180 || $coordinate > 180)) {
            throw new Exception("{$context}: Kinh độ phải trong khoảng -180 đến 180.");
        }

        return $coordinate;
    }

    /**
     * Create Point geometry from coordinates
     */
    protected function createPointGeometry(float $longitude, float $latitude)
    {
        $geoJson = [
            'type' => 'Point',
            'coordinates' => [$longitude, $latitude]
        ];

        return Congtrinh_xd::geometryFromGeoJSON(json_encode($geoJson));
    }

    /**
     * Create Congtrinh_xd record
     */
    protected function createCongtrinh_xdRecord(array $data): Congtrinh_xd
    {
        // Use the repository to create the record for consistency
        return $this->repository->create(['properties' => $data]);
    }

    /**
     * Clear Congtrinh_xd cache
     */
    protected function clearCongtrinh_xdCache(): void
    {
        $cacheTag = 'Congtrinh_xd-list';

        // Check if cache driver supports tags
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: increment cache version to invalidate all cache
            $versionKey = 'Congtrinh_xd_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
