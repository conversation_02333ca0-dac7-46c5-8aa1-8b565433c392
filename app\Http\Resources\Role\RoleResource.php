<?php

namespace App\Http\Resources\Role;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'vi_name' => $this->vi_name ?? $this->name,
            'description' => $this->description ?? null,
            'is_custom' => $this->is_custom ?? false,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            
            // Thông tin quyền
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'vi_name' => $permission->vi_name ?? $permission->name,
                        'group' => $permission->group ?? 'general',
                    ];
                });
            }),
            
            // Số lượng quyền
            'permissions_count' => $this->whenLoaded('permissions', function () {
                return $this->permissions->count();
            }),
            
            // Số lượng người dùng có vai trò này
            'users_count' => $this->whenCounted('users'),
        ];
    }
}
