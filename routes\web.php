<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\PageController;
use Illuminate\Support\Facades\Route;

// Basic web routes for backend-only application
Route::get('/', function () {
    return response()->json([
        'message' => 'Laravel Backend API',
        'version' => '1.0.0',
        'status' => 'running'
    ]);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()
    ]);
});

// Authentication routes
require __DIR__ . '/auth.php';
