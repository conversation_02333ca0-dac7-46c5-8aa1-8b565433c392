<?php

namespace App\Http\Controllers\API\Taisan;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Http\Controllers\Controller;

class AssetTemplateController extends Controller
{
    /**
     * Download asset import template file.
     */
    public function download()
    {
        $file = public_path('data/DanhSachTaiSan.xlsx');
        if (!file_exists($file)) {
            return response()->json(['message' => 'File not found.'], 404);
        }
        return response()->download($file, 'DanhSachTaiSan.xlsx');
    }
}
