<?php

namespace App\Exceptions\Role;

use Exception;

class RoleNotFoundException extends Exception
{
    public function __construct($identifier)
    {
        parent::__construct("Không tìm thấy vai trò với ID/tên: {$identifier}");
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'ROLE_NOT_FOUND'
        ], 404);
    }
}
