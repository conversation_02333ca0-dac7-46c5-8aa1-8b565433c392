# Hệ thống Activity Logs

## Tổng quan

Hệ thống Activity Logs được thiết kế để theo dõi và ghi lại tất cả các hoạt động của người dùng trong hệ thống quản lý tài sản. <PERSON>ệ thống bao gồm:

- **Backend**: API Laravel với model, controller, middleware
- **Frontend**: Component Vue.js với giao diện quản lý logs
- **Database**: Bảng activity_logs với đầy đủ thông tin tracking

## Cấu trúc Database

### Bảng `activity_logs`

```sql
- id: Primary key
- user_id: ID người dùng (foreign key to users table)
- action: Lo<PERSON><PERSON> hành động (login, logout, create, update, delete, view, export, import)
- description: <PERSON><PERSON> tả chi tiết hành động
- model_type: Loại model bị tác độ<PERSON> (nullable)
- model_id: ID của model bị tác đ<PERSON> (nullable)
- old_values: <PERSON><PERSON><PERSON>rị c<PERSON> (JSON, nullable)
- new_values: <PERSON><PERSON><PERSON> trị mới (JSON, nullable)
- ip_address: Địa chỉ IP của người dùng
- user_agent: Thông tin trình duyệt/thiết bị
- created_at: Thời gian tạo
- updated_at: Thời gian cập nhật
```

## API Endpoints

### 1. Lấy danh sách activity logs
```
GET /api/activity-logs
```

**Parameters:**
- `page`: Trang hiện tại (default: 1)
- `per_page`: Số bản ghi mỗi trang (default: 15)
- `user_id`: Lọc theo ID người dùng
- `action`: Lọc theo loại hành động
- `search`: Tìm kiếm trong mô tả
- `date_from`: Lọc từ ngày
- `date_to`: Lọc đến ngày
- `model_type`: Lọc theo loại model

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách lịch sử hoạt động thành công",
  "data": [...],
  "meta": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7,
    "from": 1,
    "to": 15
  },
  "links": {
    "first": "...",
    "last": "...",
    "prev": null,
    "next": "..."
  }
}
```

### 2. Lấy chi tiết activity log
```
GET /api/activity-logs/{id}
```

### 3. Lấy danh sách users cho filter
```
GET /api/activity-logs/filters/users
```

### 4. Lấy danh sách actions cho filter
```
GET /api/activity-logs/filters/actions
```

## Frontend Component

### LogsTab.vue

Component Vue.js được tích hợp trong trang Admin, cung cấp:

**Tính năng:**
- Hiển thị danh sách activity logs với pagination
- Tìm kiếm và lọc theo nhiều tiêu chí
- Refresh dữ liệu real-time
- Export Excel (placeholder)
- Responsive design

**Filters:**
- Từ ngày / Đến ngày
- Người dùng
- Loại hoạt động
- Tìm kiếm text trong mô tả

## Middleware Logging

### LogActivity Middleware

Middleware tự động ghi lại các hoạt động của người dùng:

**Hoạt động được ghi lại:**
- Login/Logout
- CRUD operations (Create, Read, Update, Delete)
- Export/Import operations
- View operations

**Thông tin được ghi lại:**
- User thực hiện hành động
- Loại hành động
- Mô tả chi tiết
- IP address
- User agent (browser/device info)
- Model type và ID (nếu có)

**Cấu hình:**
- Middleware được áp dụng cho tất cả API routes
- Bỏ qua một số paths không cần thiết (activity-logs, csrf-cookie, etc.)
- Error handling để không ảnh hưởng đến application

## Cách sử dụng

### 1. Truy cập Activity Logs

1. Đăng nhập vào hệ thống
2. Vào trang Admin
3. Chọn tab "Lịch Sử Hoạt Động"

### 2. Tìm kiếm và lọc

1. **Lọc theo thời gian**: Chọn từ ngày và đến ngày
2. **Lọc theo người dùng**: Chọn từ dropdown danh sách users
3. **Lọc theo loại hoạt động**: Chọn từ dropdown actions
4. **Tìm kiếm text**: Nhập từ khóa trong ô tìm kiếm
5. Nhấn "Lọc" để áp dụng hoặc "Đặt lại" để xóa filters

### 3. Xem chi tiết

- Mỗi dòng hiển thị: Thời gian, Người dùng, Hoạt động, Mô tả, IP, Thiết bị
- Hover vào dòng để highlight
- Pagination ở cuối bảng

### 4. Refresh dữ liệu

- Nhấn nút "Làm mới" để cập nhật dữ liệu mới nhất
- Icon sẽ xoay khi đang loading

## Maintenance

### 1. Dọn dẹp dữ liệu cũ

Tạo command để xóa logs cũ:

```bash
php artisan make:command CleanOldActivityLogs
```

### 2. Backup logs

Định kỳ backup bảng activity_logs để đảm bảo dữ liệu.

### 3. Monitor performance

- Theo dõi kích thước bảng activity_logs
- Tối ưu indexes nếu cần
- Cân nhắc partitioning cho dữ liệu lớn

## Troubleshooting

### 1. Logs không được ghi

- Kiểm tra middleware đã được đăng ký
- Kiểm tra user đã đăng nhập
- Xem Laravel logs để tìm lỗi

### 2. Frontend không hiển thị dữ liệu

- Kiểm tra API endpoints hoạt động
- Kiểm tra network tab trong DevTools
- Xem console logs để tìm lỗi JavaScript

### 3. Performance issues

- Thêm indexes cho các trường thường query
- Giới hạn số lượng records trả về
- Implement caching nếu cần
