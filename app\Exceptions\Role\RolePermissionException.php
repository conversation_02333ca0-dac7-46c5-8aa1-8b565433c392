<?php

namespace App\Exceptions\Role;

use Exception;

class RolePermissionException extends Exception
{
    public function __construct(string $message = "Bạn không có quyền thực hiện hành động này với vai trò")
    {
        parent::__construct($message);
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'ROLE_PERMISSION_DENIED'
        ], 403);
    }
}
