<?php

namespace App\Exceptions\Xaydung;

use Exception;

class ImportValidationException extends Exception
{
    protected $errors;
    protected $rowNumber;

    public function __construct(array $errors, int $rowNumber = 0, string $message = 'D<PERSON> liệu import không hợp lệ')
    {
        $this->errors = $errors;
        $this->rowNumber = $rowNumber;
        
        if ($rowNumber > 0) {
            $message = "Dòng {$rowNumber}: {$message}";
        }
        
        parent::__construct($message);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getRowNumber(): int
    {
        return $this->rowNumber;
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'IMPORT_VALIDATION_ERROR',
            'data' => [
                'errors' => $this->errors,
                'row_number' => $this->rowNumber
            ]
        ], 422);
    }
}
