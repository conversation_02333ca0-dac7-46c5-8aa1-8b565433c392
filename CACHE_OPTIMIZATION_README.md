# Tài liệu Tối ưu hóa Cache và Đồng bộ dữ liệu

## Tổng quan

Hệ thống đã được tái cấu trúc để đảm bảo tính nhất quán và cập nhật tức thì trên giao diện người dùng sau các thao tác CRUD (Create, Read, Update, Delete).

## Các thay đổi chính

### 1. Backend (Laravel)

#### CongdapRepository.php
- **Cache Tags**: Sử dụng cache tags với identifier `congdap-list` cho việc xóa cache có chủ đích
- **Fallback Support**: Hỗ trợ fallback cho database driver không hỗ trợ tags bằng cache versioning
- **Phương thức mới**:
  - `supportsTags()`: Kiểm tra driver có hỗ trợ tags
  - `cacheWithTags()`: <PERSON><PERSON> với tags hoặc versioning
  - `clearCacheByPattern()`: Xóa cache bằng versioning

#### CongdapController.php
- **Cache Invalidation**: Thêm logic xóa cache sau các operations `store`, `update`, `destroy`
- **Phương thức mới**:
  - `clearCongdapCache()`: Xóa cache có chủ đích với fallback support

### 2. Frontend (Vue.js)

#### Composable useDataRefresh.ts
- **Global State Management**: Quản lý việc refresh data giữa các components
- **Event System**: Hệ thống callback để thông báo khi cần refresh data

#### Map.vue
- **fetchData()**: Phương thức chung để tải dữ liệu
- **Auto Refresh**: Tự động refresh khi có thay đổi từ components khác
- **Lifecycle Management**: Đăng ký/hủy đăng ký callback khi mount/unmount

#### DataManagement.vue
- **fetchData()**: Tái cấu trúc từ `fetchCongDapData()`
- **Event Handling**: Xử lý sự kiện refresh từ component con
- **Global Trigger**: Trigger refresh cho các components khác

#### EditInfoTab.vue
- **CRUD Operations**: Implement thực tế các operations
- **Delete Functionality**: Hoàn thiện chức năng xóa với confirmation
- **Event Emission**: Emit sự kiện refresh sau mỗi operation

#### AddAssetTab.vue
- **Form Handling**: Form reactive với validation
- **API Integration**: Tích hợp với API để thêm mới
- **Geometry Support**: Hỗ trợ thêm tọa độ geometry

## Cách hoạt động

### Luồng dữ liệu khi thêm mới:
1. User điền form trong `AddAssetTab.vue`
2. Submit form → gọi API `POST /api/taisan/congdap`
3. Controller xử lý → Repository tạo record
4. Controller gọi `clearCongdapCache()` → xóa cache
5. Component emit `refresh-data` event
6. `DataManagement.vue` nhận event → gọi `fetchData()`
7. `DataManagement.vue` trigger global refresh
8. `Map.vue` nhận global refresh → gọi `fetchData()`
9. UI cập nhật với dữ liệu mới

### Luồng dữ liệu khi xóa:
1. User click delete trong `EditInfoTab.vue`
2. Confirm dialog → gọi API `DELETE /api/taisan/congdap/{id}`
3. Controller xử lý → Repository xóa record
4. Controller gọi `clearCongdapCache()` → xóa cache
5. Component emit `refresh-data` event
6. Tương tự luồng thêm mới từ bước 6

## Cache Strategy

### Với Redis/Memcached (hỗ trợ tags):
```php
Cache::tags(['congdap-list'])->flush();
```

### Với Database driver (không hỗ trợ tags):
```php
// Tăng version để invalidate cache
$versionKey = 'congdap_version';
$currentVersion = Cache::get($versionKey, 0);
Cache::put($versionKey, $currentVersion + 1, 7200);

// Cache key sẽ bao gồm version
$versionedKey = $originalKey . '_v' . $version;
```

## Testing

### Test thêm mới:
1. Mở trang Data Management
2. Chọn "Cống đập" từ sidebar
3. Chuyển sang tab "Thêm mới tài sản"
4. Điền form và submit
5. Kiểm tra dữ liệu xuất hiện ngay lập tức trong tab "Chỉnh sửa thông tin"
6. Mở trang Map và kiểm tra dữ liệu cũng được cập nhật

### Test xóa:
1. Mở trang Data Management
2. Chọn "Cống đập" từ sidebar
3. Trong tab "Chỉnh sửa thông tin", click nút xóa
4. Confirm xóa
5. Kiểm tra dữ liệu biến mất ngay lập tức
6. Mở trang Map và kiểm tra dữ liệu cũng được cập nhật

## Lưu ý

- Hệ thống tự động detect cache driver và sử dụng strategy phù hợp
- Với production, khuyến nghị sử dụng Redis để có hiệu suất tốt nhất
- Cache TTL mặc định là 1 giờ, có thể điều chỉnh trong Repository
- Global refresh chỉ hoạt động khi cả hai trang đều đang mở
